import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { FormatNumToThousands } from '/@/utils/index';
import { suppliernoPagelist } from '/@/api/common/api';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '盘点单号',
    align: 'center',
    dataIndex: 'inventoryCheckNo',
    width: 120,
    
  },
  {
    title: '盘点仓库',
    align: 'center',
    dataIndex: 'storageName',
    width: 120,
    
  },
  {
    title: '盘点表创建时间',
    align: 'center',
    dataIndex: 'checkStartTime',
    sorter: true,
    width: 120,
    
  },
  {
    title: '盘点结束时间',
    align: 'center',
    dataIndex: 'checkEndTime',
    width: 120,
    
  },
  {
    title: '盘点表创建人',
    align: 'center',
    dataIndex: 'createName',
    width: 120,
    
  },
  {
    title: '盘点人',
    align: 'center',
    dataIndex: 'checkCreateName',
    sorter: true,
    width: 100,
    
  },
  {
    title: '盘点状态',
    align: 'center',
    dataIndex: 'checkStatus_dictText',
    sorter: true,
    width: 120,
    
  },
  {
    title: '库存数量',
    align: 'center',
    dataIndex: 'inventoryTotal',
    sorter: true,
    width: 100,
  },
  {
    title: '异常盘点数量',
    align: 'center',
    dataIndex: 'errorTotal',
    width: 120,
    slots: { customRender: 'errorTotal' },
    
  },
  {
    title: '未盘点数量',
    align: 'center',
    dataIndex: 'noCheckTotal',
    width: 120,
    slots: { customRender: 'noCheckTotal' },
    
  },
  {
    title: '实盘数量',
    align: 'center',
    dataIndex: 'checkTotal',
    width: 120,
    
  },
];
//查询数据
export const formSchema: FormSchema[] = [
  {
    label: '盘点单号',
    field: 'inventoryCheckNo',
    component: 'JInput',
  },
  {
    field: 'checkStartTime',
    component: 'RangePicker',
    componentProps: { valueType: 'Date' },
    label: '盘点日期',
  },
  {
    label: '盘点表创建人',
    field: 'createName',
    component: 'Input',
    slot: 'createName',
  },
  {
    label: '盘点人',
    field: 'checkCreateName',
    component: 'Input',
    slot: 'checkCreateName',
  },
  {
    label: '盘点仓库',
    field: 'storageIds',
    component: 'ApiSelect',
    slot: 'storageIds',
  },
  {
    field: 'checkStatus', label: '盘点状态', component: 'JDictSelectTag', componentProps: () => {
      return {
        dictCode: 'inventory_check_goods_status',
      };
    },
  },
];

export const detailColumns: BasicColumn[] = [
  {
    title: '物资编码',
    align: 'center',
    dataIndex: 'goodsCode',
    width: 130,
    
  },
  {
    title: '物资名称',
    align: 'center',
    dataIndex: 'goodsName',
    width: 120,
    
    sorter: {
      multiple: 1,
    },
  },
  {
    title: '规格',
    align: 'center',
    dataIndex: 'goodsSpecs',
    width: 120,
    
    sorter: {
      multiple: 2,
    },
  },
  {
    title: '型号',
    align: 'center',
    dataIndex: 'goodsSpecsDetail',
    width: 120,
    
    sorter: {
      multiple: 3,
    },
  },
  {
    title: '单位',
    align: 'center',
    dataIndex: 'unitName',
    width: 100,
    
  },
  {
    title: '单价',
    align: 'center',
    dataIndex: 'goodsPrice',
    width: 120,
    
  },
  {
    title: '产品批号',
    align: 'center',
    dataIndex: 'batchNo',
    width: 120,
    
  },
  {
    title: '有效期',
    align: 'center',
    dataIndex: 'term',
    width: 120,
    
  },
  {
    title: '条码',
    align: 'center',
    dataIndex: 'barCode',
    width: 220,
    
  },
  {
    title: '财务分类',
    align: 'center',
    dataIndex: 'financeCategory_dictText',
    width: 100,
    
  },
  {
    title: '库存来源',
    align: 'center',
    dataIndex: 'operationType_dictText',
    width: 100,
  },
  {
    title: '条码当前状态',
    align: 'center',
    dataIndex: 'codeStatus_dictText',
    width: 120,
  },
  {
    title: '库存总数量',
    align: 'center',
    dataIndex: 'inventoryNum',
    width: 100,
    
  },
  {
    title: '锁定数量',
    align: 'center',
    dataIndex: 'lockNum',
    width: 100,
    
  },
  {
    title: '实盘数量',
    align: 'center',
    dataIndex: 'realNum',
    width: 100,
    
  },
  {
    title: '差异数量',
    align: 'center',
    dataIndex: 'errorNum',
    width: 100,
    
  },
  {
    title: '盘盈盘亏',
    align: 'center',
    dataIndex: 'checkReslutType_dictText',
    width: 100,
    
  },
  {
    title: '差异金额',
    align: 'center',
    dataIndex: 'errorAmt',
    width: 100,
    
  },
  {
    title: '盘点状态',
    align: 'center',
    dataIndex: 'checkStatus_dictText',
    width: 100,
    
  },
  {
    title: '是否异常',
    align: 'center',
    dataIndex: 'errorType_dictText',
    width: 100,
  },
  {
    title: '盘点方式',
    align: 'center',
    dataIndex: 'deviceAppType_dictText',
    width: 100,
  },
  {
    title: '是否新增异常',
    align: 'center',
    dataIndex: 'saveType_dictText',
    width: 120,
  },
  {
    title: '设备来源',
    align: 'center',
    dataIndex: 'deviceType_dictTex',
    width: 80,
  },
  {
    title: '设备类型',
    align: 'center',
    dataIndex: 'deviceAppType_dictText',
    width: 80,
  },
  {
    title: '是否新增异常',
    align: 'center',
    dataIndex: 'saveType_dictText',
    width: 120,
  },
  {
    title: '盘点人',
    align: 'center',
    dataIndex: 'createName',
    width: 80,
    
  },
  {
    title: '所在货位名称',
    align: 'center',
    dataIndex: 'locationName',
    width: 120,
  },
  {
    title: '当前货位名称',
    align: 'center',
    dataIndex: 'realLocation',
    width: 120,
  },
  {
    title: '设备名称',
    align: 'center',
    dataIndex: 'deviceName',
    width: 80,
  },
  {
    title: '盘点时间',
    align: 'center',
    dataIndex: 'updateTime',
    width: 80,
    
  },
  {
    title: '系统备注',
    align: 'center',
    dataIndex: 'errorReason',
    width: 80,
    
  },
  {
    title: '手动备注',
    align: 'center',
    dataIndex: 'notes',
    width: 80,
    
  },
  {
    title: '供应商',
    align: 'center',
    dataIndex: 'supplierName',
    width: 80,
    
  },
  {
    title: '厂商',
    align: 'center',
    dataIndex: 'manufacturerName',
    width: 80,
    
  },
];
export const detailCodeColumns: BasicColumn[] = [
  {
    title: '物资编码',
    align: 'center',
    dataIndex: 'goodsCode',
    width: 130,
    
  },
  {
    title: '物资名称',
    align: 'center',
    dataIndex: 'goodsName',
    width: 120,
    
    sorter: {
      multiple: 1,
    },
  },
  {
    title: '规格',
    align: 'center',
    dataIndex: 'goodsSpecs',
    width: 120,
    
    sorter: {
      multiple: 2,
    },
  },
  {
    title: '型号',
    align: 'center',
    dataIndex: 'goodsSpecsDetail',
    width: 120,
    
    sorter: {
      multiple: 3,
    },
  },
  {
    title: '单位',
    align: 'center',
    dataIndex: 'unitName',
    width: 100,
    
  },
  {
    title: '单价',
    align: 'center',
    dataIndex: 'goodsPrice',
    width: 100,
    
  },
  {
    title: '财务分类',
    align: 'center',
    dataIndex: 'financeCategory_dictText',
    width: 100,
    
  },
  {
    title: '库存总数量',
    align: 'center',
    dataIndex: 'inventoryNum',
    width: 120,
    
  },
  {
    title: '锁定数量',
    align: 'center',
    dataIndex: 'lockNum',
    width: 100,
    
  },
  {
    title: '实盘数量',
    align: 'center',
    dataIndex: 'realNum',
    width: 100,
    
  },
  {
    title: '差异数量',
    align: 'center',
    dataIndex: 'errorNum',
    width: 100,
    
  },
  {
    title: '差异金额',
    align: 'center',
    dataIndex: 'errorAmt',
    width: 80,
    
  },
  {
    title: '盘点状态',
    align: 'center',
    dataIndex: 'checkStatus_dictText',
    width: 80,
    
  },
  {
    title: '盘点人',
    align: 'center',
    dataIndex: 'createName',
    width: 80,
    
  },
  {
    title: '供应商',
    align: 'center',
    dataIndex: 'supplierName',
    width: 80,
    
  },
  {
    title: '厂商',
    align: 'center',
    dataIndex: 'manufacturerName',
    width: 80,
    
  },
];
export const formState: FormSchema[] = [
  {
    label: '物资编码',
    field: 'goodsCode',
    component: 'JInput',
  },
  {
    label: '物资名称',
    field: 'goodsName',
    component: 'JInput',
  },
  {
    label: '规格',
    field: 'goodsSpecs',
    component: 'JInput',
  },
  {
    label: '型号',
    field: 'goodsSpecsDetail',
    component: 'JInput',
  },
  {
    label: '产品批号',
    field: 'batchNo',
    component: 'JInput',
  },
  {
    label: '有效期',
    field: 'term',
    component: 'RangePicker',
    componentProps: { valueType: 'Date' },
  },
  {
    label: '条码',
    field: 'barCode',
    component: 'JInput',
  },
  {
    field: 'checkReslutType', label: '盘盈盘亏', component: 'JDictSelectTag', componentProps: () => {
      return {
        dictCode: 'inventory_check_status',
      };
    },
  },
  {
    field: 'checkStatus', label: '盘点状态', component: 'JDictSelectTag', componentProps: () => {
      return {
        dictCode: 'inventory_check_goods_status',
      };
    },
  },
  {
    field: 'errorType', label: '是否异常', component: 'JDictSelectTag', componentProps: () => {
      return {
        dictCode: 'yn',
      };
    },
  },
  {
    label: '盘点人',
    field: 'createName',
    component: 'Input',
    slot: 'createName',
  },
  {
    label: '供应商',
    field: 'supplierId',
    component: 'ApiSelect',
    componentProps: {
      api: suppliernoPagelist,
      labelField: 'supplierName',
      valueField: 'id',
      showSearch: true,
      optionFilterProp:"supplierNameSupplyPycode",
    },
  },
  {
    label: '厂商',
    field: 'manufacturerName',
    component: 'JInput',
  },
  {
    field: 'financeCategory', label: '财务分类', component: 'JDictSelectTag', componentProps: () => {
      return {
        dictCode: 'db_finance_category',
        mode: 'multiple',
      };
    },
    
  },
  {
    field: 'codeStatus', label: '条码当前状态', component: 'JDictSelectTag', componentProps: () => {
      return {
        dictCode: 'check_code_status',
        mode: 'multiple',
      };
    },
  },
  {
    field: 'operationType', label: '库存来源', component: 'JDictSelectTag', componentProps: () => {
      return {
        dictCode: 'operation_type',
        mode: 'multiple',
      };
    },
  },
  { field: 'locationName', label: '当前货位名称', component: 'Input',},
  { field: 'realLocation', label: '所在货位名称', component: 'Input',},
  { field: 'deviceName', label: '设备名称', component: 'Input' },
  {
    field: 'deviceAppType', label: '盘点方式', component: 'JDictSelectTag', componentProps: () => {
      return {
        dictCode: 'check_device_type',
      };
    },
  },
  {
    field: 'saveType', label: '是否异常新增', component: 'JDictSelectTag', componentProps: () => {
      return {
        dictCode: 'yn',
      };
    },
  },
];
export const formStateCode: FormSchema[] = [
  {
    label: '物资编码',
    field: 'goodsCode',
    component: 'JInput',
  },
  {
    label: '物资名称',
    field: 'goodsName',
    component: 'JInput',
  },
  {
    label: '规格',
    field: 'goodsSpecs',
    component: 'JInput',
  },
  {
    label: '型号',
    field: 'goodsSpecsDetail',
    component: 'JInput',
  },
  {
    label: '产品批号',
    field: 'batchNo',
    component: 'JInput',
  },
  {
    label: '有效期',
    field: 'term',
    component: 'RangePicker',
    componentProps: { valueType: 'Date' },
  },
  {
    label: '条码',
    field: 'barCode',
    component: 'JInput',
  },
  {
    field: 'checkReslutType', label: '盘盈盘亏', component: 'JDictSelectTag', componentProps: () => {
      return {
        dictCode: 'inventory_check_status',
      };
    },
  },
  {
    field: 'checkStatus', label: '盘点状态', component: 'JDictSelectTag', componentProps: () => {
      return {
        dictCode: 'inventory_check_goods_status',
      };
    },
  },
  {
    field: 'errorType', label: '是否异常', component: 'JDictSelectTag', componentProps: () => {
      return {
        dictCode: 'yn',
      };
    },
  },
  {
    label: '盘点人',
    field: 'createName',
    component: 'Input',
    slot: 'createName',
  },
  {
    label: '供应商',
    field: 'supplierId',
    component: 'ApiSelect',
    componentProps: {
      api: suppliernoPagelist,
      labelField: 'supplierName',
      valueField: 'id',
      showSearch: true,
      optionFilterProp:"supplierNameSupplyPycode",
    },
  },
  {
    label: '厂商',
    field: 'manufacturerName',
    component: 'JInput',
  },
  {
    field: 'financeCategory', label: '财务分类', component: 'JDictSelectTag', componentProps: () => {
      return {
        dictCode: 'db_finance_category',
        mode: 'multiple',
      };
    },
    
  },
  {
    field: 'codeStatus', label: '条码当前状态', component: 'JDictSelectTag', componentProps: () => {
      return {
        dictCode: 'check_code_status',
        mode: 'multiple',
      };
    },
  },
  {
    field: 'operationType', label: '库存来源', component: 'JDictSelectTag', componentProps: () => {
      return {
        dictCode: 'operation_type',
        mode: 'multiple',
      };
    },
  },
  { field: 'locationName', label: '当前货位名称', component: 'Input',},
  { field: 'realLocation', label: '所在货位名称', component: 'Input',},
  { field: 'deviceName', label: '设备名称', component: 'Input' },
];
