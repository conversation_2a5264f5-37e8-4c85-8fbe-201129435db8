<template>
  <div>
    <BasicModal v-bind="$attrs" @register="registerModal" title="科室退库" width="1500px" @ok="handleSubmit"
      :dragHandle="null" :maskClosable="false">
      <BasicTable id="table" bordered rowKey="id" :action-column="actionColumn" :columns="planColumns" :dataSource="innerData"
        :scroll="{ x: 1000, y: 800 }"  class="ant-table-striped" size="small" :rowClassName="rowClassName">
        <template #tableTitle>
          <span class="api-select-addon"> <span style="color: red;font-size: 16px;">*</span>目标仓库:</span>
          <ApiSelect :api="() => getspdStoragelist(StorageParmes, null)" v-model:value="targetStorageId" showSearch
            placeholder="请选择目标仓库" optionFilterProp="storageNameAbbr" labelField="storageName" valueField="id"
            style="width: 200px" @change="handleChange">
          </ApiSelect>
          <a-button id="btnOpenModal" type="primary" @click="openModals">选择物资</a-button>
          <a-input v-model:value="code" type="like" style="width: 330px" placeholder="请扫描物资条码或批次码"
            @keyup.enter="search">
            <template #addonBefore>
              <span>物资条码/批次码：</span>
            </template>
          </a-input>
          <span class="api-select-addon">退库说明:</span>
          <a-input v-model:value="remark" placeholder="退库说明" style="width: 350px" show-count :maxlength="60" />
          <div class="box-title">
            <div>注：目标仓库（仅一级库）与物资响应库房不相符</div>
          </div>
        </template>
                <template #outNum="{ record }">
          <a-input @input="changeInp(record)" v-model:value="record.outNum"
            :disabled="record.quantitativePackageFlag == 1 || record.individualFlag == 1"></a-input>
        </template>
        <template #action="{ record }">
          <TableAction :actions="[
            {
              label: '删除',
              icon: 'ic:outline-delete-outline',
              onClick: handleDel.bind(null, record),
            },
          ]" />
        </template>
      </BasicTable>
    </BasicModal>
    <MaterialSelectModal @register="regModal" @getSelectRow="setValue"></MaterialSelectModal>
  </div>
</template>
<script lang="ts" setup>
import { BasicModal, useModalInner } from "/@/components/Modal";
import { useModal } from "/@/components/Modal";
import { ApiSelect } from "/@/components/Form/index";
import MaterialSelectModal from "./MaterialSelectModal.vue";
import { ref, reactive, computed, nextTick } from "vue";
import { BasicTable, TableAction } from "/@/components/Table";
import { planColumns } from "../SpdDirectTransfer.data";
import {
  outStore,
  inventoryDetailPageList,
  getspdStoragelist,
} from "../SpdDirectTransfer.api";
import { message } from "ant-design-vue";
const targetStorageId: any = ref(null);
const targetStorageType: any = ref('');

const innerData = ref<any[]>([]);
const showFooterFlg = ref(true);
const isUpdate = ref(true);
const actionColumn: any = {
  width: 100,
  title: "操作",
  dataIndex: "action",
  slots: { customRender: "action" },
  fixed: "right",
};

let StorageParmes: any = ref({
  column: "storageType,simpleCode",
  order: "asc,asc",
  storageType_MultiString: '1,2',
  delFlag: 0,
});
let code: any = ref(null);
let remark = ref('');
const emit = defineEmits(["success"]);
//注册model
const [regModal, { openModal }] = useModal();
const openModals = (record) => {
  if (targetStorageId.value == null) {
    return message.warning("请选择目标仓库");
  }
  openModal(true, {
    record,
    targetStorageId: targetStorageId.value,
    targetStorageType: targetStorageType.value,
    isUpdate: false,
    showFooter: false,
  });
};
//表单赋值
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  innerData.value = [];
  subForm.detailList = [];
  arr.value = [];
  code.value = '';
  subForm.applyUser = undefined;
  targetStorageId.value = undefined;
  setModalProps({
    confirmLoading: false,
    showCancelBtn: !!data?.showFooter,
    showOkBtn: !!data?.showFooter,
  });
  isUpdate.value = !!data?.isUpdate;
  showFooterFlg.value = data?.showFooter;
});

/**
 * 返回勾选数据
 */

function setValue(options) {
  options.forEach((item) => {
    item.outNum = item.outNum ? item.outNum : 1;
    let arr = innerData.value.filter((v) => v.id == item.id);
    if (arr.length) {
      arr[0]["outNum"] = +arr[0]["outNum"] + +item.outNum;
    } else {
      innerData.value.push(item);
    }
  });
}


function handleChange(_, ...args) {
  targetStorageType.value = args[0].storageType;
}


/* 
扫码添加数据事件
*/
async function search() {

  if (code.value != null) {
    if (targetStorageId.value == null) return message.warning("请选择目标仓库");
    let parmes = {
      code: code.value,
      operationFlag: 1,
    };
    let res = await inventoryDetailPageList(parmes);
    if (res.length == 0) {
      message.warning("条码不正确，请重新扫码");
    } else {
      if (targetStorageId.value == res[0].storageId) {
        message.warning("当前物资所属库房与目标仓库相同，请重新扫码");
        setTimeout(() => {
          code.value = null;
        }, 200);
        return;
      }
      if (targetStorageType.value > res[0].storageType) {
       message.warning("不允许将物资从高级库退到低级库");
          setTimeout(() => {
          code.value = null;
        }, 200);
        return;
      } else if (targetStorageType.value == res[0].storageType) {
        message.warning("不允许物质在同等级库房之间平移");
           setTimeout(() => {
          code.value = null;
        }, 200);
        return;
      }

      let arr = innerData.value.filter((v) => v.id == res[0].id);
      if (arr.length) {
        message.warning("耗材已添加");
      } else {
        if (res[0].quantitativePackageFlag == 0) {
          res[0].outNum = 1;
        } else if (res[0].quantitativePackageFlag == 1) {
          res[0].outNum = res[0].quantitativePackageNum;
        }
        innerData.value.push(res[0]);
        // 添加数据后，等待DOM更新完成再滚动到新行
        nextTick(()=>{
          const tableBody = document.querySelector('#table .ant-table-body');
          if (tableBody) {
            tableBody.scrollTo({
              top: tableBody.scrollHeight,
              behavior: 'smooth' //平滑过渡
            });
          }
        });
      }
    }
  } else {
    message.warning("请输入或使用扫码枪扫码");
  }
  setTimeout(() => {
    code.value = null;
  }, 200);
}

const subForm = reactive<any>({
  applyOrderId: 0,
  applyUser: "",
  detailList: [],
});
//表单提交事件
let arr = ref<any>([]);
async function handleSubmit() {
  if (innerData.value.length > 0) {
    arr.value = [];
    setModalProps({ confirmLoading: true });
    try {
      innerData.value.forEach((item) => {
        if (item.inventoryDetailId != null) {
          const existingItem = arr.value.find(
            (detail) => detail.inventoryDetailId === item.inventoryDetailId
          );
          if (existingItem) {
            existingItem.inventoryRecordIds.push(item.id);
          } else {
            arr.value.push({
              goodsCode: item.goodsCode,
              inventoryDetailId: item.inventoryDetailId,
              inventoryRecordIds: [item.id],
              storageId: item.storageId,
            });
          }
        } else {
          arr.value.push({
            goodsCode: item.goodsCode,
            inventoryDetailId: item.id,
            inventoryDetailNum: item.outNum,
            storageId: item.storageId,
          });
        }
      });
      arr.value.forEach((item) => {
        if (item.inventoryRecordIds != null) {
          item.inventoryRecordIds = [...new Set(item.inventoryRecordIds)];
          item.inventoryDetailNum = item.inventoryRecordIds.length;
        }
      });
      // }
      let obj = {
        applyUser: subForm.applyUser,
        detailList: arr.value,
        pushFlag: 0,
        targetStorageId: targetStorageId.value, //目标仓库
        returnOrderCause: remark.value,
      };


      if (targetStorageType.value == 1 && innerData.value.some((item) => item.responseStorageId != targetStorageId.value)) return message.error("物资退库仓库与响应仓库不一致，请修改后再进行退库");

      //关闭弹窗
      await outStore(obj);
      arr.value = [];
      closeModal();
      // //刷新列表
      emit("success");
    } catch (e) {
      setModalProps({ confirmLoading: false });
    } finally {
      setModalProps({ confirmLoading: false });
    }
  } else {
    message.warning("未添加耗材，不可生成退库单");
  }
}

function handleDel(record: Recordable) {
  const index = innerData.value.findIndex((item) => item.id === record.id);
  innerData.value.splice(index, 1);
}

// 编辑事件
const changeInp = (record) => {
  record.outNum = record.outNum.replace(/[^0-9.]/g, '');
  const num = record.outNum.split('.');
  if (num.length > 1) {
    num[1] = num[1].slice(0, 4);
    record.outNum = num.join('.');
    record.outNum = record.outNum.replace(/(\.\d*?[1-9])0+$/, '$1');
  }
  if (+record.outNum > +record.currentNum) {
    record.outNum =record.currentNum;
    return message.warning('出库数量不能大于当前库存数量');
  }
};

const rowClassName = computed(() => {
  return (record, index: number) => {
    if (targetStorageType.value == 1 && record.responseStorageId !== targetStorageId.value) {
      return 'color-red';
    }
  };
});
</script>
<style lang="less" scoped>
.api-select-addon {
  margin: 0 0 4px 0;
  position: relative;
  padding: 2px 11px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: normal;
  font-size: 14px;
  text-align: center;
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 2px 0 0 2px;
  transition: all 0.3s;
}

.ant-table-striped:deep(.color-red) td {
  color: red !important;
}

.ant-table-striped:deep(.box-title) {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  /* 子元素靠右对齐 */
  align-items: center;
  /* 垂直居中（可选） */
}

.ant-table-striped:deep(.box-title) div {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: red;
}
</style>
