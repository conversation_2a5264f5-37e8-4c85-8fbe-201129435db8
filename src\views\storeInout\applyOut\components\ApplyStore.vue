<template>
  <div>
    <BasicModal v-bind="$attrs" @register="registerModal" :showCancelBtn="false" title="申领调拨出库汇总" destroyOnClose
      @ok="handleOk" :default-fullscreen="true" :maskClosable="false">
      <div class="title" style="margin-bottom: 20px;">
        <span style="margin-right:40px;">出库仓库: &nbsp;&nbsp;{{ form.store }}</span>
        <span>申领仓库：&nbsp;&nbsp;{{ form.outStore }}</span>
      </div>
      <div>扫码出库：<a-input ref="iptRef" v-model:value="form.code" @keyup.enter="addRow" style="width:300px;" />
        领用人备注：<a-input v-model:value="form.remark" @keyup.enter="addRow" style="width:300px;" /></div>
      <div style="padding:10px 0;">
        <span style=" padding-right: 20px;">是否推送</span>
        <a-switch checked-children="是" un-checked-children="否" v-model:checked="form.pushFlag" />
      </div>
      <BasicTable :clickToRowSelect="false" :rowSelection="rowSelection" bordered rowKey="id" :columns="columns"
        :dataSource="form.tableData" :scroll="{ y: 500 }" :pagination="false" class="ant-table-striped" size="small"
        :rowClassName="rowClassName">
        <template #tableTitle>
          <div class="box-title">
            <div><span class="ant-pro-table-title" style="background:  rgb(60, 240, 43) ;"></span>效期6个月内</div>
            <div><span class="ant-pro-table-title" style="background: rgb(250, 233, 157)"></span>效期3个月内</div>
            <div><span class="ant-pro-table-title" style="background:  rgb(245, 133, 133)"></span>效期1个月内(包含过期)</div>
            <div><span class="ant-pro-table-title" style="background: rgb(224, 224, 224)"></span>待出库数量为0</div>
          </div>

        </template>
        <template #packageNum="{ record }">
          <span style="color: #188ffe;" @click="openPackageDetail(record)">{{ record.packageNum }}</span>
        </template>
        <template #goodsRemark="{ record }">
          <a-input :disabled="true" v-model:value="record.goodsRemark">
            <template #suffix>
              <a-popover v-model:visible="record.visible" trigger="click" placement="right">
                <template #content>
                  <a-textarea :disabled="true" v-model:value="record.goodsRemark" placeholder="" :rows="10" />
                </template>
                <fullscreen-outlined />
              </a-popover>
            </template>
          </a-input>
        </template>

        <template #currentNum="{ record, index }">
          <span v-if="record.individualFlag === 1 && record.selected" style="color:#188ffe;cursor: pointer;"
            @click="openDetail(record, index)">{{ record.currentNum }}
          </span>
          <span v-if="record.individualFlag === 1 && !record.selected">{{ record.currentNum }}</span>
          <span v-if="record.individualFlag == 0">{{ record.currentNum }}</span>
        </template>
        <template #currentOutStoreNum="{ record }">
          <div v-if="record.individualFlag === 1 && record.quantitativePackageFlag == 0"
            style="background-color:#188ffe;color: #fff; width: 80px;height: 30px;line-height: 30px;">
            {{ record.currentOutStoreNum ? record.currentOutStoreNum : 0 }}
          </div>
           <a-input v-if="record.individualFlag != 1 && record.selected && record.quantitativePackageFlag == 0"
            v-model:value="record.currentOutStoreNum" 
            @input="handleNumberInput(record, $event)" 
            @keypress="validateNumberInput"
            @blur="formatDecimal(record)"
          />
          <div v-if="record.quantitativePackageFlag == 1">
            {{ record.currentOutStoreNum ? record.currentOutStoreNum : 0 }}
          </div>
        </template>
        <template #footer="currentPageData">
          <div class="foot pd6 dpflex jcsb">
            <div>合计</div>
            <div class="foot-total">{{ getFootTotal(currentPageData) }}</div>
          </div>
        </template>
      </BasicTable>
      <!-- 耗材Modal -->
      <GoodsModal @getCheckData="getCheckData" @register="CheckGoods"></GoodsModal>
      <PackageDetailModal @register="packageDetailModals" @success="handlePackageDetailSuccess"></PackageDetailModal>
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
import { ref, createVNode, reactive, defineEmits, computed } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicColumn } from '/@/components/Table';
import { queryPageTotalList, addByApplyOrder } from '../SpdDeliveryOut.api'
import { inventoryDetailPageList } from '/@/api/common/api';
import { BasicTable } from '/@/components/Table';
import { message } from 'ant-design-vue';
import { useModal } from '/@/components/Modal';
import { Modal } from "ant-design-vue";
import GoodsModal from './GoodsModal.vue';
import PackageDetailModal from './packageDetailModal.vue';
import { ExclamationCircleOutlined, FullscreenOutlined } from "@ant-design/icons-vue";
import { useMessage } from '/@/hooks/web/useMessage';
const { createMessage, createConfirm } = useMessage()
import { getNumToThousands } from "/@/utils/index";
const emit = defineEmits(['success', 'resetRows']);
const iptRef = ref()
const getFootTotal = (currentPageData) =>
  `${handleSummary(currentPageData).totalNum} ,\xa0` +
  `${handleSummary(currentPageData).totalAmount}`;
const [CheckGoods, { openModal: GoodsOpenModal }] = useModal(); // 耗材条码明细
const [packageDetailModals, { openModal: packageDetailOpenModal }] = useModal(); // 耗材条码明细

const searchInfo = ref(
  { column: 'goodsCode', order: 'asc' }
)
const form = reactive<any>({
  outStore: '',
  sourceStorageId: '',
  targetStorageId: '',
  pushFlag: true,
  store: '',
  code: '',
  tableData: [],
  applyOrderIds: [],
  remark: '',
})
const columns: BasicColumn[] = [
  {
    title: '物资编码',
    align: 'center',
    dataIndex: 'goodsCode',
    key: 'goodsCode',
    width: 160,
    fixed: 'left',
    ellipsis: true, // 设置ellipsis属性为true
  },
  {
    title: '物资名称',
    align: 'center',
    dataIndex: 'goodsName',
    key: 'goodsName',
    width: 160,
    ellipsis: true, // 设置ellipsis属性为true
    sorter: (a, b) => {
      const aValue = a.goodsName || '';
      const bValue = b.goodsName || '';
      return aValue.localeCompare(bValue, 'zh-Hans-CN', { numeric: true, ignorePunctuation: true });
    }
  },
  {
    title: '规格',
    align: 'center',
    dataIndex: 'goodsSpecs',
    key: 'goodsSpecs',
    width: 120,
    ellipsis: true, // 设置ellipsis属性为true
    sorter: (a, b) => {
      const aValue = a.goodsSpecs || '';
      const bValue = b.goodsSpecs || '';
      return aValue.localeCompare(bValue, 'zh-Hans-CN', { numeric: true, ignorePunctuation: true });
    }
  },
  {
    title: '型号',
    align: 'center',
    dataIndex: 'goodsSpecsDetail',
    key: 'goodsSpecsDetail',
    width: 120,
    ellipsis: true, // 设置ellipsis属性为true
    sorter: (a, b) => {
      const aValue = a.goodsSpecsDetail || '';
      const bValue = b.goodsSpecsDetail || '';
      return aValue.localeCompare(bValue, 'zh-Hans-CN', { numeric: true, ignorePunctuation: true });
    }
  },
  {
    title: '是否定数包',
    align: 'center',
    dataIndex: 'quantitativePackageFlag_dictText',
    key: 'quantitativePackageFlag_dictText',
    width: 120,
    ellipsis: true, // 设置ellipsis属性为true
    sorter: (a, b) => {
      const aValue = a.quantitativePackageFlag_dictText || '';
      const bValue = b.quantitativePackageFlag_dictText || '';
      return aValue.localeCompare(bValue, 'zh-Hans-CN', { numeric: true, ignorePunctuation: true });
    }
  },
  {
    title: '定数包规格',
    align: 'center',
    dataIndex: 'quantitativePackageSpecs',
    key: 'quantitativePackageSpecs',
    width: 120,
    ellipsis: true, // 设置ellipsis属性为true
    sorter: (a, b) => {
      const aValue = a.quantitativePackageSpecs || '';
      const bValue = b.quantitativePackageSpecs || '';
      return aValue.localeCompare(bValue, 'zh-Hans-CN', { numeric: true, ignorePunctuation: true });
    }
  },
  {
    title: '定数包数量',
    align: 'center',
    dataIndex: 'packageNum',
    slots: { customRender: 'packageNum' },
    width: 120,
  },
  {
    title: '单位',
    width: 100,
    align: 'center',
    dataIndex: 'unitName',
    key: 'unitName',
  },
   {
    title: '货位名称',
    align: 'center',
    dataIndex: 'locationName',
    width: 150,
  },
  {
    title: '申领数量',
    width: 100,
    align: 'center',
    dataIndex: 'applyNum',
    key: 'applyNum',
    sorter: (a: any, b: any) => Number(a.applyNum - 0) - Number(b.applyNum),
  },
  {
    title: '已发放数量',
    width: 120,
    align: 'center',
    dataIndex: 'distributionNum',
    key: 'distributionNum',
    sorter: (a: any, b: any) => Number(a.distributionNum - 0) - Number(b.distributionNum),
  },
  {
    title: '待出库数量',
    width: 120,
    align: 'center',
    dataIndex: 'waitDistributionNum',
    key: 'waitDistributionNum',
    sorter: (a: any, b: any) => Number(a.waitDistributionNum - 0) - Number(b.waitDistributionNum),
  },
  {
    title: '即时库存',
    width: 100,
    align: 'center',
    dataIndex: 'currentNum',
    key: 'currentNum',
    slots: { customRender: 'currentNum' },
    sorter: (a, b) => Number(a.currentNum) - Number(b.currentNum),
  },
  {
    title: '出库数量',
    width: 100,
    align: 'center',
    dataIndex: 'currentOutStoreNum',
    key: 'currentOutStoreNum',
    slots: { customRender: 'currentOutStoreNum' },
    sorter: (a, b) => Number(a.currentOutStoreNum) - Number(b.currentOutStoreNum),
  },
  {
    title: '是否个体码',
    align: 'center',
    dataIndex: 'individualFlag_dictText',
    key: 'individualFlag_dictText',
    width: 120,
    ellipsis: true, // 设置ellipsis属性为true
  },
  {
    title: '科室备注',
    align: 'center',
    dataIndex: 'goodsRemark',
    slots: { customRender: 'goodsRemark' },
    width: 120,
  },
   {
    title: '单价',
    width: 100,
    align: 'center',
    dataIndex: 'goodsPrice',
    key: 'goodsPrice',
    sorter: (a: any, b: any) => Number(a.goodsPrice - 0) - Number(b.goodsPrice),
  },

];
const [registerModal, { setModalProps, closeModal, changeOkLoading, changeLoading },] = useModalInner(async (data) => {
  setModalProps({ confirmLoading: false, showCancelBtn: !!data?.showFooter, showOkBtn: !!data?.showFooter, okText: '确认出库' });
  changeOkLoading(false)
  changeLoading(true);
  form.tableData = []
  checkedKeys.value = []
  checkedRows.value = []
  const { requestStorageName, parentStorageName, requestStorageId, parentStorageId } = data.record[0]
  iptRef.value.focus();
  form.outStore = requestStorageName
  form.store = parentStorageName
  form.sourceStorageId = parentStorageId
  form.targetStorageId = requestStorageId
  form.applyOrderIds = data.applyOrderIds
  form.remark = ''
  form.code = ''
  let str = data.applyOrderIds.join(",")
  const res = await queryPageTotalList({ applyOrderIds: str, ...searchInfo.value, })
  form.tableData = res.records
  form.tableData.forEach(item => {
    item.additionalData = []
    item.checkedKeys = []
    item.selected = false
    item.childrenList = []
    item.childrenIdList = []
  })
  changeLoading(false);
})
const openDetail = (record, index) => {
  GoodsOpenModal(true, {
    record,
    index,
    isUpdate: false,
    showFooter: true,
  });
}
const input = (record) => {
  if (+record.currentOutStoreNum > +record.currentNum) {
    record.currentOutStoreNum = 0
    message.warning('出库数量不能大于即时库存')
  }
}


// 限制只能输入数字和小数点，禁止汉字和字母
const validateNumberInput = (e) => {
  const key = e.key;
  const value = e.target.value;
  
  // 允许的控制按键
  const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab', 'Enter'];
  
  // 如果是允许的控制键，直接允许
  if (allowedKeys.includes(key)) {
    return;
  }
  
  // 使用正则表达式检查是否为数字或小数点
  if (!/^[0-9.]$/.test(key)) {
    e.preventDefault();
    return;
  }
  
  // 如果是小数点，检查是否已经存在小数点
  if (key === '.') {
    if (value.includes('.')) {
      e.preventDefault();
      return;
    }
  }
  
  // 如果已经有小数点，检查小数位数
  if (value.includes('.') && key !== '.') {
    const decimalPart = value.split('.')[1];
    if (decimalPart && decimalPart.length >= 4) {
      e.preventDefault();
      return;
    }
  }
};

// 输入处理函数，过滤非法字符
const handleNumberInput = (record, e) => {
  let value = e.target.value;
  
  // 过滤掉非数字和小数点的字符（包括汉字和字母）
  value = value.replace(/[^0-9.]/g, '');
  
  // 确保只有一个小数点
  const parts = value.split('.');
  if (parts.length > 2) {
    value = parts[0] + '.' + parts.slice(1).join('');
  }
  
  // 限制小数点后最多4位
  if (parts[1] && parts[1].length > 4) {
    value = parts[0] + '.' + parts[1].substring(0, 4);
  }
  
  // 更新值
  record.currentOutStoreNum = value;
  
  // 触发原有的 input 处理函数
  input(record);
};
// 扫码匹配
const addRow = async () => {
  if (form.code === "") {
    return;
  }
  try {
    const res = await inventoryDetailPageList({ code: form.code, storageId: form.sourceStorageId })
    if (res.length > 0) {
      if (res[0].quantitativePackageFlag == 0) {
        let term = new Date(res[0]?.term);
        let daysNum = ((term.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
        if (res.length && daysNum <= 180 && daysNum > 0) {
          Modal.confirm({
            title: () => "提示！",
            icon: () => createVNode(ExclamationCircleOutlined),
            content: () =>
              createVNode(
                "div",
                { style: "color:#000;" },
                "该物资效期剩余" + Math.floor(daysNum) + "天，是否继续？"
              ),
            onOk() {
              const scannedItem = res[0]
              let length = form.tableData.length
              for (let i = 0; i < length; i++) {
                if (form.tableData[i].additionalData.some(data => data.uniqueCode === form.code)) {
                  message.warning('扫码重复');
                } else {
                  if (form.tableData[i].goodsId === scannedItem.goodsId && form.tableData[i].matchFlag === scannedItem.matchFlag) {
                    if (form.tableData[i].currentOutStoreNum >= parseInt(form.tableData[i].currentNum) || form.tableData[i].currentOutStoreNum >= form.tableData[i].waitDistributionNum) {
                      message.warning('出库数量不能大于即时库存或待出库数量')
                      break
                    } else {
                      if (scannedItem.barcodeType == 1) {
                        return message.warning('批次码物资不支持扫码，请输入数量添加');
                      } else {
                        message.success('扫码成功');
                        form.tableData[i].additionalData.push(scannedItem);
                      }
                    }
                  }
                }
                // 保存选中的数据返回
                form.tableData[i].checkedKeys = form.tableData[i].additionalData.map(item => item.id)
                form.tableData[i].currentOutStoreNum = form.tableData[i].additionalData.length ? form.tableData[i].additionalData.length : +form.tableData[i].currentOutStoreNum
              }
            },
            onCancel() {
              iptRef.value.focus();
            },
          });
        } else if (!res.length) {
          createMessage.warning("条形码不存在");
        } else if (daysNum <= 0) {
          createMessage.error("物资已过期");
        } else {
          const scannedItem = res[0]
          let length = form.tableData.length
          for (let i = 0; i < length; i++) {
            if (form.tableData[i].additionalData.some(data => data.uniqueCode === form.code)) {
              message.warning('扫码重复');
            } else {
              if (form.tableData[i].goodsId === scannedItem.goodsId && form.tableData[i].matchFlag === scannedItem.matchFlag) {
                let bool = checkedKeys.value.some(data => data === form.tableData[i].id)
                if (!bool) {
                  checkedKeys.value.push(form.tableData[i].id)
                  form.tableData[i].selected = true
                }
                if (form.tableData[i].currentOutStoreNum >= parseInt(form.tableData[i].currentNum) || form.tableData[i].currentOutStoreNum >= form.tableData[i].waitDistributionNum) {
                  message.warning('出库数量不能大于即时库存或待出库数量')
                  break
                } else {
                  if (scannedItem.barcodeType == 1) {
                    return message.warning('批次码物资不支持扫码，请输入数量添加');
                  } else {
                    message.success('扫码成功');
                    form.tableData[i].additionalData.push(scannedItem);
                  }
                }
              }
            }
            // 保存选中的数据返回
            form.tableData[i].checkedKeys = form.tableData[i].additionalData.map(item => item.id)
            form.tableData[i].currentOutStoreNum = form.tableData[i].additionalData.length ? form.tableData[i].additionalData.length : +form.tableData[i].currentOutStoreNum
          }
        }
      } else if (res[0].quantitativePackageFlag == 1) {
        let index = -1
        form.tableData.forEach((item, i) => {
          if (item.goodsCode == res[0].goodsCode && item.quantitativePackageId === res[0].quantitativePackageId) {
            index = i
          }
        })
        if (index == -1) {
          message.info('该物资不存在,或定数包规格不符合')
        } else {
          let arr = form.tableData[index].childrenList.filter((item) => item.id === res[0].id);
          if (arr.length) {
            message.info('当前定数包已添加')
          } else {
            form.tableData[index].childrenList.push(res[0])
            form.tableData[index].childrenIdList.push(res[0].id)
            form.tableData[index].packageNum = form.tableData[index].childrenList.length
            form.tableData[index].currentOutStoreNum = (form.tableData[index].packageNum - 0) * (form.tableData[index].quantitativePackageNum - 0)
            form.tableData[index].selected = true
            checkedKeys.value.push(form.tableData[index].id)
            checkedRows.value.push(res[0])
          }
        }
      }
    } else {
      createMessage.warning("条形码不存在");
    }

  } catch (error) {
    console.log(error);
  } finally {
    form.code = '';
  }
}
const getCheckData = (e) => {
  form.tableData.forEach(item => {
    e[0]._value.forEach(v => {
      if (item.goodsId === v.goodsId) {
        item.additionalData = e[0]._value
        item.checkedKeys = e[1]._value
        item.currentOutStoreNum = item.additionalData.length
      }
    })
  });
}

function handleSummary(tableData: Recordable[]) {
  // 金额合计
  const totalAmt = tableData.reduce((prev, next) => {
    if (next.selected) {
      prev += Number(next.currentOutStoreNum * next.goodsPrice);
    }
    return prev;
  }, 0);
  // 数量总计
  const totalNumber = tableData.reduce((prev, next) => {
    if (next.selected) {
      prev += Number(next.currentOutStoreNum);
    }
    return prev;
  }, 0);
  return {
    totalAmount: `总金额 : ${getNumToThousands(totalAmt)}`,
    totalNum: `总数量 : ${totalNumber.toFixed(2)}`,
  };
}
/**
 * 选择事件
 */
const checkedKeys = ref<Array<string | number>>([]);
const checkedRows = ref<Array<string | number>>([]);
const onSelectChange = (selectedRowKeys: (string | number)[], selectionRows) => {
  checkedKeys.value = selectedRowKeys;
  checkedRows.value = selectionRows;
}
const onSelect = (record, selected, selectedRows, nativeEvent) => {
  record.selected = selected
  if (!record.individualFlag && record.quantitativePackageFlag !== 1) {
    if (parseInt(record.currentNum) > record.waitDistributionNum) {
      record.currentOutStoreNum = record.waitDistributionNum
    } else {
      record.currentOutStoreNum = record.currentNum
    }
  }
}
const onSelectAll = (selected, selectedRows, changeRows) => {
  if (selected) {
    selectedRows.forEach(row => {
      row.selected = selected
      if (!row.individualFlag && row.quantitativePackageFlag !== 1) {
        if (parseInt(row.currentNum) > row.waitDistributionNum) {
          row.currentOutStoreNum = row.waitDistributionNum
        } else {
          row.currentOutStoreNum = parseInt(row.currentNum)
        }
      }
    })
  } else {
    form.tableData.forEach(row => {
      row.selected = selected
    })
  }
}

const openPackageDetail = (record) => {
  packageDetailOpenModal(
    true, {
    record,
    isUpdate: false,
    showFooter: true,
  }
  )
}

const rowSelection = {
  type: 'checkbox',
  columnWidth: 50,
  selectedRowKeys: checkedKeys,
  onChange: onSelectChange,
  onSelect: onSelect,
  onSelectAll: onSelectAll,
  getCheckboxProps(record: Recordable) {
    // Demo: 第一行（id为0）的选择框禁用
    if (record.waitDistributionNum == 0 || record.currentNum == 0) {
      return { disabled: true };
    } else {
      return { disabled: false };
    }
  },
};
const handleOk = async () => {
  let bool = true
  checkedRows.value.forEach(item => {
    if (item.termType === 1 || item.termType === 2 || item.termType === 3 || item.termType === 4) {
      bool = false
    }
  }
  )

  if (bool) {
    handleSubmit()
  } else {
    Modal.confirm({
      title: () => "提示！",
      icon: () => createVNode(ExclamationCircleOutlined),
      content: () =>
        createVNode(
          "div",
          { style: "color:#000;" },
          "选择耗材中存在近效期物资，是否继续？"
        ),
      onOk() {
        handleSubmit()
      },
      onCancel() {
        iptRef.value.focus();
      },
    });
  }
}

const handleSubmit = async () => {
  try {
    let detailList = form.tableData.filter(item => item.selected).map(item => {
      return {
        currentOutStoreNum: item.currentOutStoreNum ? item.currentOutStoreNum : 0,
        goodsCode: item.goodsCode,
        recordIds: item.quantitativePackageFlag === 0 ? item.checkedKeys : item.childrenIdList,
        quantitativePackageQueryFlag: item.quantitativePackageFlag,
        packageNum: item.packageNum ? item.packageNum : null,
      }
    })

    let bool=true
    detailList.forEach(item => {
      if (item.currentOutStoreNum ===0||item.currentOutStoreNum === null) {
          bool = false
      }
    })
    if (!bool) {
      createMessage.warning("勾选出库物资不允许物资数量为0或者为空");
      return;
    }
    let obj = {
      applyOrderIds: form.applyOrderIds,
      sourceStorageId: form.sourceStorageId,
      targetStorageId: form.targetStorageId,
      pushFlag: +form.pushFlag,
      remark: form.remark,
      detailList,
    }
    console.log(obj, 'obj');

    changeOkLoading(true);
    await addByApplyOrder(obj)
    //回传选项和已选择的值
    //关闭弹窗
    closeModal();
    emit('resetRows', []);
    emit('success');
  } catch (e) {
    changeOkLoading(false);
  }
  finally {
    changeOkLoading(false);
  }
}

const handlePackageDetailSuccess = (res) => {
  form.tableData.forEach(item => {
    if (item.goodsCode === res.goodsCode) {
      item.childrenList = res.dataSource
      item.childrenIdList = res.dataSource.map(item => item.id)
      item.packageNum = item.childrenList.length
      item.currentOutStoreNum = (item.packageNum - 0) * (item.quantitativePackageNum - 0)
      if (item.packageNum === 0) {
        item.selected = false
        checkedKeys.value.forEach((key, index) => {
          if (key === item.id) {
            checkedKeys.value.splice(index, 1)
          }
        })
        checkedRows.value.forEach((key, index) => {
          if (key === item.id) {
            checkedRows.value.splice(index, 1)
          }
        })
      }
    }
  })
}

const rowClassName = computed(() => {
  return (record, index: number) => {
    if (record.termType === 1) {
      return 'darkGreen'
    } else if (record.termType === 2) {
      return 'darkYellow'
    } else if (record.termType === 3 || record.termType === 4 || record.termType === 5) {
      return 'darkRed'
    } else if (record.waitDistributionNum == 0 ) {
      return 'grey'
    }
  };
})




</script>
<style lang="scss" scoped>
.ant-table-striped:deep(.darkGreen) td {
  background-color: rgb(60, 240, 43) !important;
}

.ant-table-striped:deep(.darkRed) td {
  background-color: rgb(245, 133, 133) !important;
}

.ant-table-striped:deep(.darkYellow) td {
  background-color: rgb(250, 233, 157) !important;
}

.ant-table-striped:deep(.grey) td {
  background-color: rgb(224, 224, 224) !important;
}

.ant-table-striped:deep(.box-title) {
  display: flex;
  justify-content: space-between;
}

.ant-table-striped:deep(.box-title) div {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ant-table-striped:deep(.box-title) .ant-pro-table-title {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 5px;
  margin-left: 10px;
  margin-right: 10px;

}
</style>