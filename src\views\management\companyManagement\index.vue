<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <CertificationModal @register="registerModal2" @success="handleSuccess"></CertificationModal>
  </div>
</template>

<script lang="ts" name="management-companyManagement" setup>
import { BasicTable, TableAction } from '/@/components/Table'
import { useListPage } from '/@/hooks/system/useListPage'
import { columns, searchFormSchema } from './SpdCompanyManage.data'
import { list } from './SpdCompanyManage.api'
import { useModal } from '/@/components/Modal'
import CertificationModal from './components/CertificationModal.vue'
const [registerModal2, { openModal: openModal2 }] = useModal();
//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    title: '厂商数据信息',
    api: list,
    columns,
    canResize: false,
    scroll:{y:480},
    maxHeight:560,
    showActionColumn: false, //是否显示操作列
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
    },
    actionColumn: {
      width: 120,
      fixed: 'right'
    },
  },
})
const [registerTable, { reload },] = tableContext
//资质证照
function handleCertification(record: Recordable) {
  openModal2(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}
/**
 * 成功回调
 */
function handleSuccess() {
  reload();
}
/**
   * 操作栏
   */
function getTableAction(record) {
  return [
    {
      label: '资质证照',
      onClick: handleCertification.bind(null, record),
    },
  ]
}
</script>

<style lang="scss" scoped>
</style>