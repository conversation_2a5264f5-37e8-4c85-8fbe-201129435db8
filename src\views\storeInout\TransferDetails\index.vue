<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" @change="onChange">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button
          type="primary"
          preIcon="ant-design:export-outlined"
          @click="getExportXls"
          :loading="loading"
          v-auth="'store-transfer-export'"
        >
          导出</a-button
        >
      </template>
      <template #code="{ record }">
        <span style="color: #33a3dc; cursor: pointer" @click="getOpenModel(record)">
          {{ record.masterNo? record.masterNo : record.inOutNo }}
        </span>
      </template>
      <template #footer="currentPageData">
        <div class="foot pd6 dpflex jcsb">
          <div>合计</div>
          <div class="foot-total">{{ getFootTotal(currentPageData) }}</div>
        </div>
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <InventoryDetailsModal
      @register="registerModel"
      @success="handleSuccess"
    ></InventoryDetailsModal>
  </div>
</template>
<script lang="ts" name="storeInout-TransferDetails" setup>
import { BasicTable } from "/@/components/Table";
import { useModal } from "/@/components/Modal";
import { useListPage } from "/@/hooks/system/useListPage";
import { columns, searchFormSchema } from "./index.data";
import { list, getExportUrl } from "./index.api";
import InventoryDetailsModal from "./components/TransferDetailsModal.vue";
import { getNumToThousands, exportFile } from "/@/utils/index";
import { ref } from "vue";
const getFootTotal = (currentPageData) =>
  `${handleSummary(currentPageData).totalNum} ,\xa0` +
  `${handleSummary(currentPageData).totalAmount}`;
//注册drawer
const [registerModel, { openModal }] = useModal();
let mySorter = {
  column: "createTime",
  order: "desc",
};
//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    api: list,
    columns,
    canResize: false,
    scroll: { y: 480 },
    showIndexColumn: true,
    ellipsis: true,
    immediate:false,
    indexColumnProps: {
      fixed: "left",
    },
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToNumber: [],
      fieldMapToTime: [
        ["createTime", ["createTime_begin", "createTime_end"], "YYYY-MM-DD"],
      ],
    },
    showActionColumn: false,
    // 请求之前对参数做处理
    beforeFetch(params) {
      params.createTime_begin =
        params.createTime_begin == null ? null : params.createTime_begin + " 00:00:00";
      params.createTime_end =
        params.createTime_end == null ? null : params.createTime_end + " 23:59:59";
    },
  },
});
const [registerTable, { reload, getForm }] = tableContext;
function handleSummary(tableData: Recordable[]) {
  // 金额合计
  const totalAmt = tableData.reduce((prev, next) => {
    prev += Number(next.totalAmt);
    return prev;
  }, 0);
  // 数量总计
  const totalNumber = tableData.reduce((prev, next) => {
    prev += Number(next.detailNum);
    return prev;
  }, 0);
  return {
    totalAmount: `本页总金额 : ${getNumToThousands(totalAmt)}`,
    totalNum: `本页总数量 : ${totalNumber.toFixed(2)}`,
  };
}
const loading = ref(false);
const onExportXls = async () => {
  let myParams = getForm().getFieldsValue();
  let params: any = { ...myParams, ...mySorter };
  params.createTime_begin =
    params.createTime_begin == null ? null : params.createTime_begin + " 00:00:00";
  params.createTime_end =
    params.createTime_end == null ? null : params.createTime_end + " 23:59:59";
  params.column = params.column ? params.column : "createTime";
  params.order = params.order ? params.order : "desc";
  await getExportUrl(params).then((res) => {
      exportFile(res,'移库明细');
  });
};
const getExportXls = async () => {
  loading.value = true;
  await onExportXls();
  loading.value = false;
};
const onChange = (_pagination, _filters, sorter) => {
  mySorter.column = sorter.field;
  if (sorter.order == "ascend") {
    mySorter.order = "asc";
  } else {
    mySorter.order = "desc";
  }
};
function handleSuccess() {
  reload();
}
/**
 * 弹窗弹出
 */
const getOpenModel = (record) => {
  console.log("打开当前耗材所在入库单明细", record);
  openModal(true, {
    record,
    isUpdate: false,
    showFooter: false,
  });
};
</script>

<style scoped></style>
