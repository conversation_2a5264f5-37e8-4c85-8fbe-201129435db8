<template>
  <BasicModal @register="registerModal" destroyOnClose :maskClosable="false" :keyboard="false">
    <div style="display: flex;justify-content: space-evenly;padding-top: 50px;">
      <span class="text">上传文件:</span>
      <a-upload name="file" accept=".xls,.xlsx" :multiple="false" :fileList="fileList" :remove="handleRemove"
        :max-count="1" :beforeUpload="beforeUpload">
        <a-button class="export-btn" type="primary" preIcon="ant-design:upload-outlined">导入文件</a-button>
      </a-upload>
    </div>
    <template #footer>
      <a-button @click="closeModal">取消</a-button>
      <a-button :loading="loading" type="primary" @click="handleSubmit">确定</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, Ref, reactive } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { useMessage } from '/@/hooks/web/useMessage';
import { importExcel } from "../index.api";
import axios from 'axios';
import { getToken, getTenantId } from '/@/utils/auth';
import { uploadUrl } from '/@/hooks/setting/config';
import { useGlobSetting } from '/@/hooks/setting';
const { createMessage, createWarningModal } = useMessage()
const glob = useGlobSetting();
const $emit = defineEmits(['success']);
//文件集合
const fileList: Ref<File[]> = ref([]);
let formData = new FormData()
const params = reactive<any>({
})
const loading = ref(false)
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  params.outStorageId = data.storageId
  clearFormData(formData);
  fileList.value = []
  loading.value = false
  setModalProps({
    width: 600,
  });
})
//上传前处理
const clearFormData = (formData) => {
  for (var key of formData.keys()) {
    formData.delete(key);
  }
}
//移除上传文件
const handleRemove = (file) => {
  const index = unref(fileList).indexOf(file);
  const newFileList = unref(fileList).slice();
  newFileList.splice(index, 1);
  fileList.value = newFileList;
}
//上传前
const beforeUpload = (file) => {
  fileList.value = []
  clearFormData(formData);
  formData.append('file', file);
  fileList.value = [...unref(fileList), file];
  return false;
}
const handleSubmit = async () => {
  if (!unref(fileList).length) return createMessage.error('请上传文件')
  loading.value = true
  axios.post(uploadUrl.uploadUrl + importExcel, formData, {
    headers: {
      'X-Access-Token': getToken(),
      'Tenant-Id': getTenantId()
    },
  }).then(res => {
    if (res.data.code === 201) {
      let { message, result: { msg, fileUrl, fileName }, }: any = res.data;
      let href = glob.uploadUrl + fileUrl;
      createWarningModal({
        title: message,
        centered: false,
        content: `
          <div>
            <span>${msg}</span><br/> 
            <span>具体详情请<a href = ${href} download = ${fileName}> 点击下载 </a> </span> 
          </div>
          `,
      });
    } else if (res.data.code === 200) {
      createMessage.success(res?.data?.message);
      $emit('success')
      closeModal()
    } else {
      createMessage.error(res?.data?.message);
    }
  }).catch(err => {
  }).finally(() => {
    loading.value = false
  })

}

</script>
<style lang="scss" scoped>
.text {
  font-size: 16px;

  &::after {
    // 添加 ::after 伪元素的属性
    content: '*';
    position: absolute;
    left: 110px;
    color: red;
  }
}

:deep(.ant-col) {
  width: auto;
}

.ant-form {
  text-align: center;
}

.export-btn {
  width: 200px;
}
</style>
