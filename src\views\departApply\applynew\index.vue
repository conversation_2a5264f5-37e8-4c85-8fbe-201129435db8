<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined" v-if="props.formtype == '0'"> 新增申领单</a-button>
        <a-button type="primary" @click="batchApproval" v-if="props.formtype == '1'" v-auth="'applyManage:approval'"> 审核通过</a-button>
        <a-button type="primary" @click="batchReturnback" v-if="props.formtype == '1'" v-auth="'applyManage:returnback'"> 退回</a-button>
        <a-button type="primary" @click="handleBack" v-if="props.formtype == '1'" v-auth="'applyManage:back'"> 撤回</a-button>
        <a-button type="primary" @click="handleExcleImport" v-if="props.formtype == '1'" v-auth="'applyManage:excleImport'"> 导出</a-button>
    
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
      <template #footer="currentPageData">
        <div class="foot pd6 dpflex jcsb">
          <div>合计</div>
          <div class="dpflex">
            <div class="ml100">{{ `${checkSumAmount}` }}</div>
            <div class="foot-total">{{ getFootTotal(currentPageData) }}</div>
        </div>
      </div>
    </template>
    </BasicTable>
    <!-- 表单区域 -->
    <CreateApplynewModal @register="registerCreatePlanModal" @success="handleSuccess" />
    <ApplyPrint :printData="printData" :list="listdata" ref="printRef"/>
  </div>
</template>

<script lang="ts" name="departApply-applynew" setup>
import { ref , computed } from 'vue';
import { BasicTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { useListPage } from '/@/hooks/system/useListPage'
import { columns, searchFormSchema } from './Applynew.data';
import { list, deleteOne, auditBatch, audit ,queryApplyOrderPrint,firstAuditBatch, exportForIds, getLastMonthCheckStatus} from './Applynew.api';
import CreateApplynewModal from './components/CreateApplynewModal.vue';
import ApplyPrint from './components/ApplyPrint.vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { handleSummaryNew, exportFile, checkedSum } from '/@/utils/index'
const getFootTotal = (currentPageData)=> handleSummaryNew(currentPageData,'totalAmt','applyOrderNum')
const checkSumAmount = computed(() => checkedSum(selectedRows.value,'totalAmt','applyOrderNum')) // 勾选合计
const { createMessage,createConfirm} = useMessage();
const props = defineProps({
  formtype: {
    type: String,
    default: '0'
  }
})

const printData = ref<any>({})
const listdata = ref<any>([])
const printRef = ref()

//注册model
const [registerCreatePlanModal, { openModal }] = useModal();
//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    title: '申领单列表',
    api: list,
    columns,
    canResize: false,
    scroll:{y:480},
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToNumber: [
      ],
      fieldMapToTime: [
        ['applyCreateTime', ['applyCreateTime_begin', 'applyCreateTime_end'], 'YYYY-MM-DD']
      ],
    },
    actionColumn: {
      width: 240,
      fixed: 'right'
    },
    // 请求之前对参数做处理
    beforeFetch(params) {
      params.parentApplyOrderId  = -1,
      params.auditStatus_MultiString = props.formtype == '0' ? "" : "1,2,3,4,5";
    },
  },
})
const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext


async function handleAdd() {
  const modalConfig = {
    isUpdate: false,
    showFooter: true,
    isDisabled: false,
  };
  const res = await getLastMonthCheckStatus() //查询是否有未完成的盘点任务
  if (!res) {
    createConfirm({
      title: '未完成盘点提醒',
      content: '当前仓库上一个月内无盘点记录，请尽快完成月盘！',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        openModal(true, modalConfig);
      }
    });
    return;
  }
  openModal(true, modalConfig);
}
/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: true,
    isDisabled: false,
  });
}
/**
 * 详情
*/
function handleDetail(record: Recordable) {
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
    isDisabled: true,
  });
}
/**
 * 打印事件
 */
 async function handlePrint(record: Recordable) {
  const res = await queryApplyOrderPrint({ ids: record.id,size:7 })
  listdata.value = res;
  setTimeout(function () {
    printRef.value.print();
  }, 100);
  return
}
/**
 * 删除事件
 */
async function handleDelete(record) {
  await deleteOne({ id: record.id }, handleSuccess);
}

/**
 * 提交事件
 */
async function handleSubmit(record) {
  await audit({ ids: [record.id] ,auditStatus:'1'}, handleSuccess);
}

/**
 * 撤回事件
 */
async function handleCancel(record) {
  await audit({ ids: [record.id],auditStatus:'0' }, handleSuccess);
}

/**
 * 审核通过
 */
 async function handleAudit(record) {
  await firstAuditBatch({ ids: [record.id] ,auditStatus:'5'});
  handleSuccess()
}
 async function handleApproval(record) {
  await audit({ ids: [record.id] ,auditStatus:'2'}, handleSuccess);
}

/**
 * 退回
 */
async function handleReturnback(record) {
  await audit({ ids: [record.id],auditStatus:'3' }, handleSuccess);
}

/**
 * 终结
 */
 async function handleEnd(record) {
  await audit({ ids: [record.id], auditStatus:'4' }, handleSuccess);
}

/**
 * 批量审核通过
 */
async function batchApproval() {
  if(selectedRowKeys.value.length <= 0){
    createMessage.warning("请先选择需要操作的数据！");
  }else{
    let items = selectedRows.value.filter(o=>o.auditStatus != 1);
    if(items.length > 0){
      createMessage.warning("只能选择待审核状态数据，进行审核");
    }else{
      await auditBatch({ ids: selectedRowKeys.value,auditStatus:'2' },'审核通过', handleSuccess);
    }
    
  }
}

/**
 * 批量退回
 */
async function batchReturnback() {
  if(selectedRowKeys.value.length <= 0){
    createMessage.warning("请先选择需要操作的数据！");
  }else{
    let items = selectedRows.value.filter(o=>o.auditStatus != 1);
    if(items.length > 0){
      createMessage.warning("只能选择待审核状态数据，进行审核");
    }else{
      await auditBatch({ ids: selectedRowKeys.value,auditStatus:'3'  },'退回', handleSuccess);
    }
  }
}
const handleBack = async ()=>{
  if(!selectedRowKeys.value.length) return createMessage.warning("请先选择需要操作的数据！")
  let isAudit = selectedRows.value.filter(o=>o.auditStatus === 1);
  if(isAudit.length) return createMessage.warning("存在未审核的申领单，不允许撤回")
  await auditBatch({ ids: selectedRowKeys.value,auditStatus:'0'  },'撤回', handleSuccess);
}
const handleExcleImport = async () => {
  if(!selectedRowKeys.value.length) return createMessage.error("请先选择需要导出的申领单！")
  let params = { 
    id_MultiString: selectedRowKeys.value.join(','),
    column:'createTime',
    order:'desc',
   }
  const res = await exportForIds(params)
  exportFile(res)
}

/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}
/**
 * 操作栏
 */
let str = '12345657'
function getTableAction(record) {
  return [
    {
      label: '查看',
      onClick: handleDetail.bind(null, record),
    },{
      label: '编辑',
      ifShow: props.formtype == '0' && (record.auditStatus == '0'||record.auditStatus == '3') ? true : false,
      onClick: handleEdit.bind(null, record),
    }, {
      label: '删除',
      ifShow: props.formtype == '0' && (record.auditStatus == '0'||record.auditStatus == '3') ? true : false,
      popConfirm: {
        title: '是否确认删除',
        confirm: handleDelete.bind(null, record),
      }
    }, {
      label: '提交',
      ifShow: props.formtype == '0' && (record.auditStatus == '0'||record.auditStatus == '3') ? true : false,
      popConfirm: {
        title: '是否确认提交',
        confirm: handleSubmit.bind(null, record),
      }
    },{
      label: '撤回',
      ifShow: props.formtype == '0' && record.auditStatus == '1' ? true : false,
      popConfirm: {
        title: '是否确认撤回',
        confirm: handleCancel.bind(null, record),
      }
    },
    {
      label: '审核', //一级审核按钮
      ifShow: (props.formtype == '0' && record.auditStatus == '7') && record.multilevelAuditFlag != '0' ? true : false,
      popConfirm: {
        title: '是否确认审核',
        confirm: handleAudit.bind(null, record),
      },
      auth: 'apply:audit',
    },
    {
      label: '打印',
      ifShow: props.formtype == '0' ,
      onClick: handlePrint.bind(null, record),
    },{ //区分一级审核 和直接审核
      label: '审核通过',
      ifShow: props.formtype == '1' &&((record.auditStatus == '5' && record.multilevelAuditFlag == '1') || record.auditStatus == '1' && record.multilevelAuditFlag == '0') ? true : false,  
      auth:'applyManage:approval',
      popConfirm: {
        title: '是否确认审核通过',
        confirm: handleApproval.bind(null, record),
      }
    }, {
      label: '退回',
      ifShow: props.formtype == '1' && ((record.auditStatus == '5' && record.multilevelAuditFlag == '1') || record.auditStatus == '1' && record.multilevelAuditFlag == '0') ? true : false,
      auth:'applyManage:returnback',
      popConfirm: {
        title: '是否确认退回',
        confirm: handleReturnback.bind(null, record),
      }
    },{
      label: '终结申领',
      ifShow: props.formtype == '1' && record.auditStatus == '2' ? true : false,
      auth:'applyManage:end',
      popConfirm: {
        title: '是否确认终结申领',
        confirm: handleEnd.bind(null, record),
      }
    }
  ]
}



</script>

<style scoped lang="scss">
.foot{
  .foot-total{
    text-align: right;
  }
}
:v-deep(.ant-table-body){
  overflow: hidden;
}
</style>