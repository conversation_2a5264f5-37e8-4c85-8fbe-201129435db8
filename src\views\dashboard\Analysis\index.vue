<template>
  <div class="box">
    <h1 class="h1">欢迎使用华兴SPD物流系统</h1>
  </div>
  <div class="expire-warning">
    <div class="left">
      <div class="header">效期预警</div>
      <div style="margin-top: 20px;padding-bottom: 50px;">
        <div style=" width: 96%;display: flex;justify-content: space-evenly;margin-bottom: 20px;">
          <div v-for="item in column" class="w25" :style="{ visibility: (item.title === '1' ? 'hidden' : 'visible') }">
            {{ item.title }}
          </div>
        </div>
        <div style="width: 96%;display: flex;justify-content: space-evenly;margin-bottom: 20px;">
          <div class="w25">已过期物资</div>
          <div @click.prevent="jump(0)" class="w25 red">{{ expireData.categoryNum }}</div>
          <div @click.prevent="jump(0)" class="w25 red">{{ expireData.totalNum }}</div>
          <div @click.prevent="jump(0)" class="w25 red">{{ expireData.totalAmt }}</div>
        </div>
        <div style=" width: 96%;display: flex;justify-content: space-evenly;">
          <div class="w25">3个月近效期物资</div>
          <div @click.prevent="jump(1)" class="w25 yellow">{{ effectData.categoryNum }}</div>
          <div @click.prevent="jump(1)" class="w25 yellow">{{ effectData.totalNum }}</div>
          <div @click.prevent="jump(1)" class="w25 yellow">{{ effectData.totalAmt }}</div>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="header dpflex jcsb" style="padding: 20px 15px;">
        <span>未计费重点需关注</span>
        <span @click="jumpPage" class="detail-link">详情>></span>
      </div>
      <div style="margin-top: 20px;padding: 0 20px 20px 20px;">
        <BasicTable @register="registerTable"></BasicTable>
      </div>
    </div>

  </div>
</template>
<script lang="ts" setup>
import { BasicTable, useTable } from "/@/components/Table";
import { useRouter } from 'vue-router';
import { indexTerm, list } from './api'
import { onMounted, ref } from 'vue';
import { useUserStore } from "/@/store/modules/user";
import type { BasicColumn } from '/@/components/Table';

const columns: BasicColumn[] = [
  {
    title: '科室',
    align: 'center',
    dataIndex: 'requestDepartName',
  },
  {
    title: '患者姓名',
    align: 'center',
    dataIndex: 'patientName',
  },
  {
    title: '病历号',
    align: 'center',
    dataIndex: 'liveHospitalCode',
  },
  {
    title: '医生',
    align: 'center',
    dataIndex: 'doctorName',
  },
];

const currentPage = ref(1);// 当前页
const loading = ref(false);
const hasMore = ref(true);// 是否加载
const tableData = ref<any[]>([]);
const departAllFlag = ref(0)
//
const loadInitialData = async () => {
  const res = await list({ pageNo: 1, pageSize: 10,departId:departId.value }); res?.result?.records
  departAllFlag.value = res?.departAllFlag
  if (res?.result?.records) {
    tableData.value = res?.result?.records;
    hasMore.value = res?.result?.records.length === 10;
  }
};

// 加载
const loadMore = async () => {
  if (loading.value || !hasMore.value) return;
  
  loading.value = true;
  try {
    const res = await list({ 
      pageNo: currentPage.value + 1, 
      pageSize: 10 ,
      departId:departId.value
    });
    
    if (res?.result?.records.length) {
      tableData.value = [...tableData.value, ...res?.result?.records];
      currentPage.value++;
      hasMore.value = res?.result?.records.length === 10;
    } else {
      hasMore.value = false;
    }
  } finally {
    loading.value = false;
  }
};

// 表格注册
const [registerTable] = useTable({
  showIndexColumn: true,
  columns,
  pagination: false, 
  scroll: { y: 160 },  
  maxHeight: 160,
  rowKey: 'id',
  dataSource: tableData,  
  immediate: false,  
});

// 监听表格滚动并加载初始数据
onMounted(async () => {
  // 加载初始数据
  await loadInitialData();
  
  const tableEl = document.querySelector('.ant-table-body');
  if (tableEl) {
    tableEl.addEventListener('scroll', async (e: Event) => {
      const target = e.target as HTMLElement;
      const { scrollTop, scrollHeight, clientHeight } = target;
      
      // 距底部100px
      if (scrollHeight - scrollTop - clientHeight < 100 && !loading.value) {
        await loadMore();
      }
    });
  }
});

const userStore: any = useUserStore();

let storage_id = ref(userStore.hospitalZoneInfo?.storage?.id)
const departId = ref(userStore.hospitalZoneInfo.depart.id)
const column = [
  { title: '1', },
  { title: '物资类别', },
  { title: '物资数量', },
  { title: '总金额', },
]
const expireData = ref({
  indexFlag: '',
  categoryNum: '',
  totalNum: '',
  totalAmt: '',
})//过期
const effectData = ref({
  indexFlag: '',
  categoryNum: '',
  totalNum: '',
  totalAmt: '',

})//有效期

const getList = async () => {
  const res = await indexTerm();
  expireData.value = res?.result[0]
  effectData.value = res?.result[1]
}
getList()
const router = useRouter();
const routes = router.getRoutes();
const jump = (type) => {
  const arr = routes.filter(item => item.path === '/fineReport_spd_31');
  if (arr.length > 0 && arr[0].meta && arr[0].meta.frameSrc) {
    arr[0].meta.frameSrc = arr[0].meta.frameSrc.replace(/([&]term=[^&]*)/, '');
    arr[0].meta.frameSrc += type === 0 ? `&storage_id=${storage_id.value}&term=0` : `&storage_id=${storage_id.value}&term=1`;
  }
  router.push('/fineReport_spd_31');
}
const jumpPage = ()=> {
  router.push({ name: 'operationGrant-OperationGrantDetailQuery', params:{ isJump: 1,departId:departId.value,departAllFlag:departAllFlag.value }})
}
</script>
<style scoped lang="scss">
.w25 {
  width: 25%;
  text-align: center;
  color: #2f4084;
}

.red {
  background-color: #fdacab;
  color: #2f4084;
  text-decoration: underline;
  padding: 6px 0;
}

.yellow {
  background-color: #fdebab;
  color: #2f4084;
  text-decoration: underline;
  padding: 4px 0;
}

.box {
  width: 100%;
  height: 70%;
  background-color: #E4EAFC;
  background-image: url(../../../assets/images/indexPG.png);
  background-size: 30%;
  background-repeat: no-repeat;
  background-position: center center;
  position: relative;

  .h1 {
    font-size: 30px;
    font-weight: 700;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 4%;
    color: #1b2d77;
  }
}

.header {
  background-color: #e7ebfc;
  padding: 20px 0 20px 100px;
  color: #2f4084;
  font-size: 16px;
}
.expire-warning {
  width: 98%;
  margin: auto;
  display: flex;
  justify-content: space-around;
  font-weight: bold;
  color: #333;
  transform: translate(0, -70px);

  .left {
    width: 49.2%;
    border: 2px solid #ccd2fe;
    border-radius: 8px 8px 0 0;
  }

  .right {
    width: 49.2%;
    border: 2px solid #ccd2fe;
    border-radius: 8px 8px 0 0;
    .detail-link{
      font-size: 14px;
      color: #387bff;
      cursor: pointer;
    }
  }
}
</style>
