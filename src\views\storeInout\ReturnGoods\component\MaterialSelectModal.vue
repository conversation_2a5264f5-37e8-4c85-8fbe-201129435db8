<template>
  <div>
    <BasicModal
      v-bind="$attrs"
      @register="registerModal"
      title="选择耗材"
      :width="1200"
      @ok="handleOk"
      destroyOnClose
      :maskClosable="false"
      defaultFullscreen
    >
      <BasicTable
        bordered
        size="small"
        rowKey="id"
        :canResize="false"
        :columns="MaterialColumns"
        :dataSource="innerData"
        :action-column="actionColumn"
        :scroll="{ x: 1000, y: 300 }"
        :row-selection="rowSelection"
        :pagination="true"
        :clickToRowSelect="true"
        @edit-change="onEditChange"
      >
        <template #tableTitle>
          <a-input
            v-model:value="searchData.code"
            style="width: 330px"
            placeholder="请输入耗材条码/批次码"
            @keyup.enter="addRow"
          >
            <template #addonBefore>
              <span>耗材条码/批次码：</span>
            </template>
          </a-input>
          <a-input
            v-model:value="searchData.goodsCode"
            style="width: 300px"
            placeholder="请输入耗材编码"
            @keyup.enter="addRow"
          >
            <template #addonBefore>
              <span>耗材编码：</span>
            </template>
          </a-input>
          <a-input
            v-model:value="searchData.goodsName"
            style="width: 300px"
            placeholder="请输入名称"
            @keyup.enter="addRow"
          >
            <template #addonBefore>
              <span>耗材名称：</span>
            </template>
          </a-input>
          <a-input
            v-model:value="searchData.goodsSpecs"
            style="width: 300px"
            placeholder="请输入规格"
            @keyup.enter="addRow"
          >
            <template #addonBefore>
              <span>规格型号：</span>
            </template>
          </a-input>
          <a-input
            v-model:value="searchData.supplierName"
            style="width: 300px"
            placeholder="请输入供应商名称"
            @keyup.enter="addRow"
          >
            <template #addonBefore>
              <span>供应商：</span>
            </template>
          </a-input>
          <a-button type="primary" preIcon="ant-design:search-outlined" @click="search"
            >查询</a-button
          >
          <a-button type="primary" preIcon="ant-design:reload-outlined" @click="resets"
            >重置</a-button
          >
        </template>
        <template #returnPurchaseNum="{ record }">
          <a-input @input="changeInp(record)" v-model:value="record.returnPurchaseNum" :disabled="record.quantitativePackageFlag==1||record.individualFlag==1"></a-input>
        </template>

      </BasicTable>
    </BasicModal>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, defineEmits } from "vue";
import { BasicModal, useModalInner } from "/@/components/Modal";
import { BasicTable, TableAction } from "/@/components/Table";
import { MaterialColumns } from "../SpdDirectTransfer.data";
import { inventoryDetailPageList } from "../SpdDirectTransfer.api";
import { message } from "ant-design-vue";
/** */
const innerData = ref<any[]>([]);
let searchData = reactive({
  goodsCode: "",
  goodsName: "",
  goodsSpecs: "",
  code: "",
  storageId: "",
  supplierName: "",
  quantitativePackageQueryFlag:1
});
//赋值
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  searchData.goodsCode = "";
  searchData.goodsName = "";
  searchData.goodsSpecs = "";
  searchData.code = "";
  searchData.supplierName = "";
  searchData.storageId = data.sourceStorageId;

  innerData.value = [];
  checkedRows.value = [];
  checkedKeys.value = [];
  search()
});

const search = async () => {
  
  let data: any = JSON.parse(JSON.stringify(searchData));
  for (const key in data) {
    if (data[key] && key != "storageId"&& key != "code"&& key != "quantitativePackageQueryFlag") {
      data[key] = "*" + data[key] + "*";
    }
  }
  const res = await inventoryDetailPageList(data);
  console.log(res);
  res.forEach((item) => {
    if (item.individualFlag == 1) {
      item.returnPurchaseNum=1
};
  });
  innerData.value = res;
};
const resets = () => {
  searchData.goodsCode = "";
  searchData.goodsName = "";
  searchData.goodsSpecs = "";
  searchData.code = "";
  searchData.supplierName = "";
  innerData.value = [];
  checkedRows.value = [];
};
// 编辑事件
function onEditChange({ column, value, record }) {
  if (column.dataIndex === "returnPurchaseNum") {
    // 本例
    const temp = innerData.value.filter((item) => item.id === record.id);
    if (temp.length >= 1 && value >= 0) {
      if (value <= 0) {
        value = 1;
        message.warning("出库数量不能小于1，已更改为数量1");
      } else if (value > record.currentNum) {
        value = record.currentNum;
        message.warning("出库数量不能大于库存，已更改为最大数量");
      }
      temp[0].returnPurchaseNum = value;
    }
  }
}
const changeInp = (record) => {
    record.returnPurchaseNum = record.returnPurchaseNum.replace(/[^0-9.]/g, '');
  const num = record.returnPurchaseNum.split('.');
  if (num.length > 1) {
    num[1] = num[1].slice(0, 4);
    record.returnPurchaseNum = num.join('.');
    record.returnPurchaseNum = record.returnPurchaseNum.replace(/(\.\d*?[1-9])0+$/, '$1');
  }
  if (+record.returnPurchaseNum > +record.currentNum) {
    record.returnPurchaseNum = 0
    return message.warning('出库数量不能大于当前库存数量')
  }
}
/**
 * 选择列配置
 */
const checkedKeys = ref<Array<string | number>>([]);
const checkedRows = ref<Array<string | number>>([]);
const rowSelection = {
  type: "checkbox",
  columnWidth: 50,
  selectedRowKeys: checkedKeys,
  onChange: onSelectChange,
};
/**
 * 选择事件
 */
function onSelectChange(selectedRowKeys: (string | number)[], selectionRows) {
  checkedKeys.value = selectedRowKeys;
  checkedRows.value = selectionRows;
}
async function addRow() {
  try {
  } catch (error) {;
  } finally {
    // rfid.value = '';
    search();

  }
}
// function handleDel(record: Recordable) {
//   const index = innerData.value.findIndex((item) => item.id === record.id);
//   innerData.value.splice(index, 1);
// }
/**
 *
 */
const emit = defineEmits(["getSelectResult", "getSelectRow", "reset"]);
function handleOk() {
  if (checkedRows.value.length == 0) {
    return message.warning("请选择耗材");
  }
  emit("getSelectRow", checkedRows.value);
  emit("reset");
  //关闭弹窗
  closeModal();
}
</script>
