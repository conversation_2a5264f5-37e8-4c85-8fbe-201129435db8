<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <template #form-dlTypeName="{ model, field }">
        <a-select v-model:value="model[field]" style="width: 100%" :options="options" allowClear
          placeholder="请选择带量采购分类">
        </a-select>
      </template>
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleAdd">新增物资</a-button>
        <a-button class="ml10" type="primary" preIcon="ant-design:plus-outlined"
          @click="handleAddType">新增带量采购分类</a-button>
        <a-button class="ml10" type="primary" preIcon="ant-design:upload-outlined"
          @click="handleImport">Excel导入</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <AddQuantityMaterial @register="AddQuantityMaterialModal" @success="reload()"></AddQuantityMaterial>
    <a-modal title="新增带量采购类型" v-model:visible="visible" @ok="handleOk" :confirm-loading="confirmLoading"
      :maskClosable="false" :keyboard="false">
      <div style="height: 160px;margin: 20px;">
        带量采购类型： <a-input v-model:value="dlTypeName" style="width: 300px" placeholder="请输入带量采购类型" />
      </div>
    </a-modal>
    <ExportModal @register="exportModals" @success="reload()"></ExportModal>
  </div>
</template>

<script lang="ts" name="quantityReport-quantityMaterialsConfig" setup>
import { ref } from 'vue';
import { BasicTable, TableAction } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage';
import { useModal } from '/@/components/Modal';
import { columns, Schema } from './index.data';
import { list, addDlType, getDlType, deleteDlGoods } from './index.api';
import AddQuantityMaterial from './components/addQuantityMaterial.vue';
import ExportModal from "./components/ExportModal.vue";
import { useMessage } from '/@/hooks/web/useMessage';
const options = ref()
const getTypeList = async () => {
  const res = await getDlType()
  options.value = res.map(v => {
    return {
      label: v.dlTypeName,
      value: v.id
    }
  });
}
getTypeList()
const { createMessage } = useMessage();
const [AddQuantityMaterialModal, { openModal: openAddQuantityMaterialModal }] = useModal();
const [exportModals, { openModal: openExportModal }] = useModal();
const visible = ref<boolean>(false);
const confirmLoading = ref<boolean>(false);
const dlTypeName = ref<string>('')
const { tableContext, } = useListPage({
  tableProps: {
    api: list,
    columns,
    canResize: false,
    scroll: { y: 480 },
    formConfig: {
      labelCol: {
        xxl: 8
      },
      schemas: Schema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToTime: [
        ['createTime', ['createTime_begin', 'createTime_end'], 'YYYY-MM-DD'],
      ],
    },
    showIndexColumn: true,
    actionColumn: {
      width: 100,
      fixed: 'right'
    },
  },
})
const [registerTable, { reload }] = tableContext
const getTableAction = (record) => {
  return [
    {
      label: "查看",
      onClick: handleDetail.bind(null, record),
    },
    {
      label: "编辑",
      onClick: handleEdit.bind(null, record),
    },
    {
      label: "删除",
      popConfirm: {
        title: '是否确认删除',
        confirm: handleDel.bind(null, record,),
      },
    },

  ]
}
const handleAddType = () => {
  visible.value = true
  dlTypeName.value = ''
}
const handleImport = () => {
  openExportModal(true, {})
}
const handleOk = async () => {
  if (!dlTypeName.value) return createMessage.error('请输入带量采购类型')
  confirmLoading.value = true;
  await addDlType({ dlTypeName: dlTypeName.value })
  confirmLoading.value = false;
  visible.value = false
  reload()
  getTypeList()
}
const handleDetail = (record) => {
  openAddQuantityMaterialModal(true, {
    type: 'detail',
    record
  })
}
const handleEdit = (record) => {
  openAddQuantityMaterialModal(true, {
    type: 'edit',
    record
  })
}
const handleDel = async (record) => {
  await deleteDlGoods(`/spd/dl/deleteDlGoods?id=${record.id}`);
  reload();
}
const handleAdd = () => {
  openAddQuantityMaterialModal(true, {
    type: 'add',
  })
}

</script>

<style scoped lang="scss"></style>
