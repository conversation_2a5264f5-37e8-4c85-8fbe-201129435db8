<template>
  <div class="table-container">
    <!-- 查询区域 -->
    <div class="table-header">
      <div class="table-but-slot">
        <slot name="tableButtons"></slot>
      </div>
      <div class="table-search-bar" v-if="props.searchable">
        <a-input v-model:value="searchGoodsName" placeholder="请输入查询内容" style="width: 200px; margin-right: 10px" @pressEnter="handleSearchEnter" />
        <a-button type="primary" @click="handleSearch">查询</a-button>
        <a-button style="margin-left: 10px" @click="clearSearch">清空</a-button>
      </div>
    </div>

    <JVxeTable
      ref="tableRef"
      stripe
      :toolbar="false"
      :rowNumber="true"
      :rowSelection="false"
      :rowExpand="false"
      :border="true"
      size="small"
      resizable
      asyncRemove
      :clickSelectRow="false"
      :height="props.tableHeight"
      :checkboxConfig="{ range: true }"
      :loading="loading"
      :disabled="disabled"
      :columns="props.columns"
      :dataSource="dataSource"
      :highlightCurrentRow="true"
      :currentRow="currentRow"
      :reloadEffect="true"
      @remove="onJVxeRemove"
      @valueChange="onchange"
      @sort-change="onSortChange"
    >
      <template #myAction="props">
        <Popconfirm title="确定删除吗？" @confirm="onDeleteRow(props)" v-if="props.row.goodsCode">
          <a>删除</a>
        </Popconfirm>
      </template>
    </JVxeTable>
    <!-- 子表单 -->
    <PopupTable
      v-model:visible="popupVisible"
      :title="'数据列表'"
      :query-param="popupQueryParam"
      :query-url="props.popupQueryUrl"
      :columns="props.popupColumns"
      :trigger-element="getTriggerElement()"
      @select="onPopupSelect"
      @cancel="onPopupCancel"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick, onMounted, onBeforeUnmount } from 'vue';
// noinspection ES6UnusedImports
import { Popconfirm, Input, Button, message } from 'ant-design-vue';
import { JVxeTypes, JVxeTableInstance } from '/@/components/jeecg/JVxeTable/types';
import { useMessage } from '/@/hooks/web/useMessage';
import PopupTable from './PopupTable.vue'; // 修改导入方式
import { number } from 'vue-types';

const { createMessage } = useMessage();
const tableRef = ref<JVxeTableInstance>();
const loading = ref(false);
const disabled = ref(false);
const popupVisible = ref(false); // 弹窗可见性
const popupQueryParam = ref({}); // 弹窗查询参数
const popupDebounceTimer = ref<number | null>(null); // 防抖计时器
let rowClickValue; // 行点击值

// 查询相关变量
const searchGoodsCode = ref('');
const searchGoodsName = ref('');
const highlightRowIndex = ref<number | null>(null);
const originalDataSource = ref<any[]>([]);
const currentRow = ref<any>(null); // 添加这行用于控制当前高亮行

// 添加搜索结果相关变量
const searchResults = ref<number[]>([]); // 存储所有匹配项的索引
const currentSearchIndex = ref<number>(-1); // 当前高亮项在搜索结果中的索引

// 添加变量用于保存当前排序状态
const currentSortState = ref({
  column: null,
  property: null,
  order: null,
});

// 添加变量用于跟踪是否按下了回车键
const enterPressed = ref(false);

const props = defineProps({
  // 弹窗查询URL
  popupQueryUrl: {
    type: String,
    default: '/spd/spdGoodsCommon/goodsBizVoList',
  },
  searchable: {
    type: Boolean,
    default: true,
  },
  tableHeight: {
    type: number,
    default: 500,
  },
  //查询Key值
  queryParam: {
    type: String,
    required: true,
    default: 'goodsName__goodsSimpleCode__goodsCommonName__goodsCode__goodsSpecs_orFlag',
  },
  numberField: {
    type: String,
    default: 'number_float',
  },

  columns: {
    type: Array,
    required: true,
    default: () => [
      {
        title: '物资编码',
        key: 'goodsCode',
        type: JVxeTypes.normal,
        width: 180,
        align: 'center',
        fixed: 'left',
        sortable: true,
      },
      {
        title: '物资名称',
        key: 'goodsName',
        type: JVxeTypes.input,
        width: 280,
        align: 'center',
        sortable: true,
      },
      {
        title: '货位',
        key: 'locationName',
        type: JVxeTypes.normal,
        width: 120,
        align: 'center',
        sortable: true,
      },
      {
        title: '规格',
        key: 'goodsSpecs',
        type: JVxeTypes.normal,
        width: 280,
        align: 'center',
        sortable: true,
      },
      {
        title: '型号',
        key: 'goodsSpecsDetail',
        type: JVxeTypes.normal,
        width: 280,
        align: 'center',
        sortable: true,
      },
      {
        title: '计量单位',
        key: 'unitName',
        type: JVxeTypes.normal,
        width: 100,
        align: 'center',
        sortable: true,
      },
      //增加小数输入列，可输入浮点数病统计列
      {
        title: '数量',
        key: 'number_float',
        type: JVxeTypes.floatInput,
        width: 180,
        statistics: ['sum'],
        align: 'center',
        sortable: true,
      },
      {
        title: '单价',
        key: 'goodsPrice',
        type: JVxeTypes.normal,
        width: 100,
        sortable: true,
      },
      {
        title: '包装单位',
        key: 'packageUnit',
        type: JVxeTypes.normal,
        width: 100,
        align: 'center',
        sortable: true,
      },

      {
        title: '品牌',
        key: 'brand',
        type: JVxeTypes.normal,
        width: 180,
        align: 'center',
        sortable: true,
      },
      {
        title: '供应商',
        key: 'supplierName',
        type: JVxeTypes.normal,
        width: 280,
        sortable: true,
      },
      {
        title: '生产厂商',
        key: 'manufacturerName',
        type: JVxeTypes.normal,
        width: 280,
        sortable: true,
      },
      {
        title: '操作',
        key: 'action',
        type: JVxeTypes.slot,
        fixed: 'right',
        minWidth: 100,
        align: 'center',
        slotName: 'myAction',
      },
    ],
  },
  popupColumns: {
    type: Array,
    required: true,
    default: () => [
      {
        title: '物资编码',
        key: 'goodsCode',
        width: 180,
      },
      {
        title: '物资名称',
        key: 'goodsName',
        width: 150,
      },
      {
        title: '别名',
        key: 'goodsCommonName',
        width: 120,
      },

      {
        title: '规格',
        key: 'goodsSpecs',
        width: 200,
      },
      {
        title: '型号',
        key: 'goodsSpecsDetail',
        width: 250,
      },
      {
        title: '库存',
        key: 'currentNum',
        width: 100,
      },
      {
        title: '单位',
        key: 'unitName',
        width: 80,
      },
      {
        title: '单价',
        key: 'goodsPrice',
        width: 100,
      },
      {
        title: '包装单位',
        key: 'packageUnit',
        width: 100,
      },

      {
        title: '品牌',
        key: 'brand',
        width: 120,
      },
      {
        title: '生产厂商',
        key: 'manufacturerName',
        width: 250,
      },
      {
        title: '操作',
        key: 'action',
        type: JVxeTypes.slot,
        fixed: 'right',
        minWidth: 100,
        align: 'center',
        slotName: 'myAction',
      },
    ],
  },
});

// 表格数据源
const dataSource = ref<any[]>([]);
//随机生成数据
//for (let i = 0; i < 10001; i++) {
//  dataSource.value.push({
//  })
//}
//页面初始化生成一条空数据
setTimeout(() => {
  tableRef.value!.addRows();
}, 100);

async function onDeleteRow(props) {
  //使用map循环找到dataSource中的数据对应的数据并删除
  let index = dataSource.value.findIndex((item) => item.id === props.row.id);
  if (index != -1) {
    // 保存当前排序状态
    const savedSortState = { ...currentSortState.value };

    // 清空排序状态
    originalDataSource.value = [];

    // 执行删除操作
    dataSource.value.splice(index, 1);
    const xTable = tableRef.value!.getXTable();
    console.log(dataSource.value);
    //检查最后一行是不是空行，如果是则删除

    xTable.loadData(dataSource.value);
    // 检查最后一行是否为空行，如果不是则添加空行
    //if (dataSource.value.length === 0 || dataSource.value[dataSource.value.length - 1].goodsCode) {
    //  dataSource.value.push({});
    //}
    //
    //xTable.loadData(dataSource.value);

    // 如果之前有排序状态，则按照之前的排序条件重新排序
    //if (savedSortState.column && savedSortState.property && savedSortState.order) {
    // 重新应用排序
    setTimeout(() => {
      applySort(savedSortState.column, savedSortState.property, savedSortState.order);
    }, 0);
    //}

    message.success('删除成功');
  } else {
    message.error('删除失败');
  }
}
const onchange = async (value) => {
  // 只有当单元格的值发生变化并且不为空时才触发弹窗
  // 仅在goodsName列变化时触发弹窗
  if (value.value && value.value != value.oldValue && value.column.key == 'goodsName') {
    // 保存当前点击的单元格值
    rowClickValue = value;
    // 检查事件来源，避免失焦时触发
    const activeElement = document.activeElement;
    // 如果当前焦点在输入框上，说明是用户正在输入，可以触发
    // 如果焦点不在输入框上，可能是失焦触发的，避免触发弹窗
    const isInputElementFocused = activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA');

    // 如果是失焦导致的change事件，则不触发弹窗
    if (!isInputElementFocused) {
      // 检查是否是程序触发的change事件（如数据初始化等）
      // 可以通过检查事件的详细信息来判断
      return;
    }

    // 只有在按下回车键时才触发弹窗
    if (!enterPressed.value) {
      return;
    }

    // 重置回车键状态
    enterPressed.value = false;
  } else if (value.column.key === props.numberField) {
    // 监听键盘事件在JVxeFloatInputCell中处理
    // 这里我们只处理逻辑跳转
    console.log('Number input changed:', value);
  }
};

// 处理弹窗选择结果
const onPopupSelect = (selectedRow) => {
  createMessage.success('已选择数据');
  // 在这里可以处理选中的数据，例如填充到表格中
  let index = rowClickValue.row.rowIndex;
  let bool = true;
  dataSource.value.forEach((item) => {
    if (item.goodsId == selectedRow.goodsId) {
      bool = false;
    }
  });
  if (bool) {
    if (dataSource.value.length > 0 && !dataSource.value[dataSource.value.length - 1].goodsCode) {
      dataSource.value.splice(dataSource.value.length - 1, 1);
    }
    if (!index) {
      selectedRow.rowIndex = (dataSource.value.length + 1).toString();
      selectedRow.requestNum = 1;
      dataSource.value.push(selectedRow);
    } else {
      selectedRow.rowIndex = index.toString();
      selectedRow.requestNum = 1;
      dataSource.value[index - 1] = selectedRow;
    }

    const xTable = tableRef.value!.getXTable();
    xTable.loadData(dataSource.value);
    setTimeout(() => {
      tableRef.value!.addRows();
    }, 100);
    // 关闭弹窗
    popupVisible.value = false;

    // 聚焦到数量输入框
    setTimeout(() => {
      console.log(index);

      // 获取当前行的number_float单元格并聚焦
      const rowIndex = index ? index - 1 : dataSource.value.length - 1; // 计算实际行索引
      if (xTable) {
        // 通过vxe-table的API获取单元格并聚焦
        const rowRecord = dataSource.value[rowIndex];
        const column = props.columns.find((col) => col.key === props.numberField);
        console.log(props.numberField);
        
        if (rowRecord && column) {
          xTable.setActiveCell(rowRecord, props.numberField);
        }
      }
    }, 100);
  } else {
    createMessage.warning(selectedRow.goodsName + '已添加');
  }
};

// 处理弹窗取消事件（按ESC键或点击关闭按钮）
const onPopupCancel = () => {
  // 将焦点返回到触发弹窗的输入框
  console.log(rowClickValue, rowClickValue.cellType);
  if (rowClickValue && rowClickValue.cellType) {
    // 通过 rowClickValue 获取原始输入框并聚焦
    const xTable = tableRef.value!.getXTable();

    if (xTable) {
      nextTick(() => {
        // 聚焦到原始输入框
        xTable.setActiveCell(rowClickValue.row, rowClickValue.column.key);
      });
    }
  }
};

// 获取触发元素的位置信息
const getTriggerElement = () => {
  // 这里可以根据实际需要获取触发元素
  // 例如通过event.target获取当前输入框元素
  return document.activeElement;
};

/**
 * 处理查询功能
 * 根据输入的查询条件在父组件传递的columns所有字段中查找符合条件的数据，滚动到第一条的位置并高亮显示
 * 不过滤数据，仅滚动并高亮
 */
const handleSearch = (moveToNext = false) => {
  originalDataSource.value = [...dataSource.value];

  // 如果没有输入查询条件，则清除高亮
  if (!searchGoodsName.value) {
    clearSearchResults();
    return;
  }

  // 获取所有可搜索的字段名（从父组件传递的columns中提取key）
  const searchableKeys = props.columns.filter((column: any) => column.key && typeof column.key === 'string').map((column: any) => column.key);
  // 查找所有匹配的项
  const matchedIndices = originalDataSource.value
    .map((item, index) => ({ item, index }))
    .filter(({ item }) => {
      // 检查每一行数据是否在任意字段中包含查询关键词
      return searchableKeys.some((key) => {
        const value = item[key];
        // 确保值存在且为字符串或可转为字符串
        if (value !== null && value !== undefined) {
          return String(value).includes(searchGoodsName.value);
        }
        return false;
      });
    })
    .map(({ index }) => index);

  // 如果没有匹配项
  if (matchedIndices.length === 0) {
    clearSearchResults();
    createMessage.warning('未找到匹配的数据');
    return;
  }

  // 更新搜索结果
  searchResults.value = matchedIndices;

  // 如果是移动到下一个匹配项
  if (moveToNext) {
    // 移动到下一个匹配项
    currentSearchIndex.value = (currentSearchIndex.value + 1) % matchedIndices.length;
  } else {
    // 默认移动到第一个匹配项
    currentSearchIndex.value = 0;
  }

  // 获取要高亮的索引
  const targetIndex = matchedIndices[currentSearchIndex.value];
  highlightRowIndex.value = targetIndex;

  // 使用nextTick确保DOM更新后再执行滚动
  nextTick(() => {
    // 通过JVxeTable的API滚动到指定行
    const xTable = tableRef.value?.getXTable();
    if (xTable) {
      // 获取匹配的行数据
      const targetRow = originalDataSource.value[targetIndex];
      // 设置当前行以实现高亮
      currentRow.value = targetRow;
      // 滚动到目标行
      xTable.scrollToRow(targetRow);
      console.log(targetRow);

      // 使用vxe-table的setCurrentRow方法设置当前行
      setTimeout(() => {
        try {
          // 尝试使用vxe-table的API设置当前行
          xTable.setCurrentRow(targetRow);
          xTable.refreshScroll();
          console.log('设置当前行成功');
        } catch (e) {
          console.log('设置当前行失败:', e);
        }
      }, 50);
    }

    const position = currentSearchIndex.value + 1;
    const total = matchedIndices.length;
    createMessage.info(`定位到第 ${position} 个匹配项，共 ${total} 个`);
  });
};
/**
 * 处理回车键搜索
 */
const handleSearchEnter = () => {
  // 如果已经有搜索结果且当前有高亮项，则移动到下一个
  if (searchResults.value.length > 0 && highlightRowIndex.value !== null) {
    handleSearch(true);
  } else {
    // 否则执行初始搜索
    handleSearch(false);
  }
};

/**
 * 清空搜索结果
 */
const clearSearchResults = () => {
  highlightRowIndex.value = null;
  searchResults.value = [];
  currentSearchIndex.value = -1;

  // 清除当前行设置
  nextTick(() => {
    const xTable = tableRef.value?.getXTable();
    if (xTable) {
      try {
        xTable.setCurrentRow(null);
      } catch (e) {
        console.log('清除当前行设置失败:', e);
      }
    }
  });
};

/**
 * 清空查询条件
 */
const clearSearch = () => {
  searchGoodsCode.value = '';
  searchGoodsName.value = '';
  clearSearchResults();
  const xTable = tableRef.value!.getXTable();
  xTable.loadData(dataSource.value);

  setTimeout(() => {
    tableRef.value!.addRows();
  }, 100);
};

// 排序处理函数
const onSortChange = ({ column, property, order, sortBy, sortList, $event }) => {
  // 保存当前排序状态
  currentSortState.value = { column, property, order };

  // 确保只对启用了排序的列进行排序
  if (!column.sortable) return;

  // 保存当前数据顺序作为原始顺序（如果是第一次排序）
  if (originalDataSource.value.length === 0) {
    originalDataSource.value = [...dataSource.value];
  }

  // 应用排序
  applySort(column, property, order);
};

// 应用排序的函数
const applySort = (column, property, order) => {
  // 检查是否有空行（最后一行是否为空行）
  let emptyRow = null;
  const hasEmptyRow = dataSource.value.length > 0 && !dataSource.value[dataSource.value.length - 1].goodsCode;
  if (hasEmptyRow) {
    // 取出最后一行空行
    emptyRow = dataSource.value.pop();
  }

  // 如果是取消排序，恢复原始顺序
  if (order === 'default') {
    dataSource.value = [...originalDataSource.value];
    // 如果原来有空行，确保空行在最后
    if (hasEmptyRow) {
      // 检查当前数据末尾是否为空行，如果不是则添加
      if (dataSource.value.length > 0 && dataSource.value[dataSource.value.length - 1].goodsCode) {
        dataSource.value.push({});
      }
    }
  }
  // 如果是正序排序
  else if (order === 'asc') {
    dataSource.value.sort((a, b) => {
      if (!a[property] && !b[property]) return 0;
      if (!a[property]) return -1;
      if (!b[property]) return 1;
      if (a[property] < b[property]) return -1;
      if (a[property] > b[property]) return 1;
      return 0;
    });
  }
  // 如果是倒序排序
  else if (order === 'desc') {
    dataSource.value.sort((a, b) => {
      if (!a[property] && !b[property]) return 0;
      if (!a[property]) return 1;
      if (!b[property]) return -1;
      if (a[property] < b[property]) return 1;
      if (a[property] > b[property]) return -1;
      return 0;
    });
  }

  // 如果原来有空行，重新添加到末尾
  if (hasEmptyRow) {
    // 确保末尾是空行
    if (dataSource.value.length > 0 && dataSource.value[dataSource.value.length - 1].goodsCode) {
      dataSource.value.push({});
    } else if (dataSource.value.length === 0) {
      dataSource.value.push({});
    }
  }
  console.log(dataSource.value);

  // 更新表格数据
  if (tableRef.value) {
    const xTable = tableRef.value.getXTable();
    xTable.loadData(dataSource.value);
    //setTimeout(() => {
    //  tableRef.value!.addRows();
    //}, 100);
  }
};

// 添加获取当前数据源的方法，用于父组件获取数据
const getCurrentDataSource = () => {
  return dataSource.value;
};
// 添加导入数据的方法，用于父组件向子组件导入数据
const importDataSource = (data) => {
  if (Array.isArray(data)) {
    // 清空现有数据
    dataSource.value = [];

    // 导入新数据
    dataSource.value.push(...data);

    // 更新表格数据
    if (tableRef.value) {
      const xTable = tableRef.value.getXTable();
      xTable.loadData(dataSource.value);
    }
    setTimeout(() => {
      tableRef.value!.addRows();
    }, 100);
    return { success: true, message: `成功导入 ${data.length} 条数据` };
  } else {
    return { success: false, message: '导入数据失败，数据格式不正确' };
  }
};

// 暴露方法给父组件使用
defineExpose({
  getCurrentDataSource,
  importDataSource,
});

// 在页面初始化时保存原始数据顺序
watch(dataSource, (newVal) => {
  if (originalDataSource.value.length === 0 && newVal.length > 0) {
    originalDataSource.value = [...newVal];
  }
});

// 添加键盘事件处理相关变量
let keydownHandler: any = null;

// 在组件挂载时添加键盘事件监听器
onMounted(() => {
  keydownHandler = (event) => {
    // 检查是否在goodsName列中按下回车键
    if (event.key === 'Enter') {
      const activeElement = document.activeElement;
      if (activeElement) {
        // 检查当前焦点是否在goodsName列的输入框中
        const cellElement = activeElement.closest('.vxe-body--column');
        if (cellElement) {
          // 获取列的field属性
          const colid = cellElement.getAttribute('colid');
          if (colid && tableRef.value) {
            const xTable = tableRef.value.getXTable();
            const column = xTable.getColumnById(colid);

            // 如果是在goodsName列按下回车
            if (column && column.property === 'goodsName') {
              event.preventDefault();
              event.stopPropagation();
              // 设置回车键状态为true
              enterPressed.value = true;

              // 获取当前输入框的值
              const inputValue = activeElement.value;

              // 如果有当前输入的值，则触发弹窗
              if (inputValue) {
                // 设置查询参数
                popupQueryParam.value = {
                  [props.queryParam]: '*' + inputValue + '*',
                };
                // 显示弹窗
                popupVisible.value = true;
              }
            }
            // 检查当前焦点是否在number_float列的输入框中
            else if (column && column.property === props.numberField) {
              event.preventDefault();
              event.stopPropagation();
              // 更新表格数据
              if (tableRef.value && event.target.value.length > 0) {
                const xTable = tableRef.value.getXTable();
                xTable.loadData(dataSource.value);
                setTimeout(() => {
                  tableRef.value!.addRows();
                }, 100);
              }
            }
          }
        }
      }
    }
  };

  // 添加全局键盘事件监听器
  document.addEventListener('keydown', keydownHandler);
});

// 在组件卸载前移除键盘事件监听器
onBeforeUnmount(() => {
  if (keydownHandler) {
    document.removeEventListener('keydown', keydownHandler);
  }
});
</script>

<style scoped>
.table-container {
  position: relative;
  margin: 10px;
}
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-search-bar {
  z-index: 10;
  background: #fff;
  padding: 10px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  text-align: right;
}

/* 使用更通用的选择器来确保高亮效果 */
.vxe-table .vxe-body--row.row--current,
.vxe-table .vxe-body--row.row--current td {
  background-color: #57a5f3 !important;
}

/* 备用高亮样式 */
.highlight-row,
.highlight-row td {
  background-color: #64a8ec !important;
}

/* 增强排序符号的样式 */
.vxe-table .vxe-header--column.sortable .vxe-header--sort-icon {
  font-size: 14px !important;
  color: #000 !important;
  opacity: 1 !important;
  margin: 0 2px !important;
}

/* 正序排序时的样式 */
.vxe-table .vxe-header--column.sortable.asc .vxe-header--sort-icon {
  color: #057ef0 !important;
  transform: rotate(0deg) !important;
}

/* 倒序排序时的样式 */
.vxe-table .vxe-header--column.sortable.desc .vxe-header--sort-icon {
  color: #1890ff !important;
  transform: rotate(180deg) !important;
}

/* 未排序状态的样式 */
.vxe-table .vxe-header--column.sortable.default .vxe-header--sort-icon {
  color: #000000 !important;
  opacity: 0.7 !important;
}

/* 确保排序图标在所有情况下都可见 */
.vxe-table .vxe-header--column.sortable .vxe-header--sort-icon {
  display: inline-block !important;
  width: 14px !important;
  height: 14px !important;
  line-height: 14px !important;
  text-align: center !important;
}
</style>