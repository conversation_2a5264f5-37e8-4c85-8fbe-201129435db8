{"name": "jeecgboot-vue3", "version": "3.4.0", "author": {"name": "jeecg", "email": "jeec<PERSON>@163.com", "url": "https://github.com/jeecgboot/jeecgboot-vue3"}, "scripts": {"bootstrap": "yarn install", "serve": "npm run dev", "dev": "vite", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 vite build && esno ./build/script/postBuild.ts", "build:test": "cross-env vite build --mode test && esno ./build/script/postBuild.ts", "build:no-cache": "yarn clean:cache && npm run build", "gen:icon": "esno ./build/generate/icon/index.ts", "report": "cross-env REPORT=true npm run build", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "npm run build && vite preview", "preview:dist": "vite preview", "log": "conventional-changelog -p angular -i CHANGELOG.md -s", "clean:lib": "rimraf node_modules", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:pretty": "pretty-quick --staged", "test:unit": "jest", "test:unit-coverage": "jest --coverage", "test:gzip": "http-server dist --cors --gzip -c-1", "test:br": "http-server dist --cors --brotli -c-1", "reinstall": "rimraf yarn.lock && rimraf package.lock.json && rimraf node_modules && npm run bootstrap"}, "dependencies": {"@element-plus/icons": "^0.0.11", "@element-plus/icons-vue": "^0.2.4", "@iconify/iconify": "^2.0.4", "@jeecg/online": "1.0.1", "@vueuse/core": "^6.6.2", "@zxcvbn-ts/core": "^1.0.0-beta.0", "ant-design-vue": "3.2.12", "axios": "^0.23.0", "bpmn-js": "^8.10.0", "bpmn-js-task-resize": "^1.2.0", "bpmn-js-token-simulation": "^0.10.0", "china-area-data": "^5.0.1", "clipboard": "^2.0.8", "codemirror": "^5.63.3", "codemirror-editor-vue3": "^2.0.6", "core-js": "^3.6.5", "cron-parser": "^3.5.0", "cropperjs": "^1.5.12", "crypto-js": "^4.1.1", "dayjs": "1.11.10", "diagram-js-minimap": "^2.1.1", "dom-align": "^1.12.2", "echarts": "^5.2.1", "element-plus": "^2.2.1", "enquire.js": "^2.1.6", "intro.js": "^4.2.2", "jsbarcode": "^3.11.5", "lint": "^0.7.0", "lodash-es": "^4.17.21", "md5": "^2.3.0", "min-dash": "^3.5.2", "mockjs": "^1.1.0", "moment": "2.29.1", "nprogress": "^0.2.0", "parse-myudi": "^1.0.1", "path-to-regexp": "^6.2.0", "pinia": "2.0.0-rc.14", "print-js": "^1.6.0", "qrcode": "^1.4.4", "qrcodejs2": "0.0.2", "qrcodejs2-fix": "^0.0.1", "resize-observer-polyfill": "^1.5.1", "showdown": "^1.9.1", "sortablejs": "^1.14.0", "tinymce": "^5.10.3", "vditor": "^3.8.13", "vue": "^3.2.20", "vue-cropper": "^0.5.6", "vue-cropperjs": "^5.0.0", "vue-draggable-resizable": "^2.3.0", "vue-i18n": "^9.1.9", "vue-infinite-scroll": "^2.0.2", "vue-json-pretty": "^2.0.4", "vue-print-nb-jeecg": "^1.0.11", "vue-router": "^4.0.12", "vue-types": "^4.1.1", "vuedraggable": "^4.1.0", "vxe-table": "4.1.0", "vxe-table-plugin-antd": "^3.0.3", "xe-utils": "^3.3.1", "xss": "^1.0.13"}, "devDependencies": {"@commitlint/cli": "^13.2.1", "@commitlint/config-conventional": "^13.2.0", "@iconify/json": "^1.1.399", "@purge-icons/generated": "^0.7.0", "@types/codemirror": "^5.60.5", "@types/crypto-js": "^4.0.2", "@types/fs-extra": "^9.0.13", "@types/inquirer": "^8.1.3", "@types/intro.js": "^3.0.2", "@types/jest": "^27.0.2", "@types/lodash-es": "^4.17.5", "@types/mockjs": "^1.0.4", "@types/node": "^16.11.1", "@types/nprogress": "^0.2.0", "@types/qrcode": "^1.4.1", "@types/qs": "^6.9.7", "@types/showdown": "^1.9.4", "@types/sortablejs": "^1.10.7", "@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "@vitejs/plugin-legacy": "^1.6.2", "@vitejs/plugin-vue": "^2.3.3", "@vitejs/plugin-vue-jsx": "^1.2.0", "@vue/compiler-sfc": "3.2.20", "@vue/eslint-config-prettier": "^6.0.0", "@vue/test-utils": "^2.0.0-rc.16", "autoprefixer": "^10.3.7", "babel-eslint": "^10.1.0", "commitizen": "^4.2.4", "conventional-changelog-cli": "^2.1.1", "cross-env": "^7.0.3", "dotenv": "^10.0.0", "eslint": "^8.0.1", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^8.3.0", "eslint-define-config": "^1.1.1", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^25.2.2", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^7.19.1", "esno": "^0.10.1", "fs-extra": "^10.0.0", "http-server": "^14.0.0", "inquirer": "^8.2.0", "is-ci": "^3.0.0", "jest": "^27.3.1", "less": "^4.1.2", "lint-staged": "^11.2.3", "npm-run-all": "^4.1.5", "postcss": "^8.3.9", "prettier": "^2.4.1", "pretty-quick": "^3.1.1", "rimraf": "^3.0.2", "rollup-plugin-visualizer": "5.5.2", "sass": "^1.32.12", "stylelint": "^13.13.1", "stylelint-config-prettier": "^9.0.3", "stylelint-config-standard": "^22.0.0", "stylelint-order": "^4.1.0", "ts-jest": "^27.0.7", "ts-node": "^10.3.0", "typescript": "^4.4.4", "vite": "^2.6.10", "vite-plugin-compression": "^0.3.5", "vite-plugin-html": "^2.1.0", "vite-plugin-imagemin": "^0.4.6", "vite-plugin-mock": "^2.9.6", "vite-plugin-optimize-persist": "^0.1.2", "vite-plugin-package-config": "^0.1.1", "vite-plugin-purge-icons": "^0.7.0", "vite-plugin-pwa": "^0.11.3", "vite-plugin-style-import": "^1.2.1", "vite-plugin-svg-icons": "^1.0.5", "vite-plugin-theme": "^0.8.1", "vite-plugin-vue-setup-extend": "^0.1.0", "vite-plugin-windicss": "^1.4.12", "vue-eslint-parser": "^8.0.0", "vue-tsc": "^0.28.7"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china", "rollup": "^2.56.3"}, "repository": {"type": "git", "url": "git+https://github.com/jeecgboot/jeecgboot-vue3.git"}, "license": "MIT", "bugs": {"url": "https://github.com/jeecgboot/jeecgboot-vue3/issues"}, "homepage": "https://github.com/jeecgboot/jeecgboot-vue3", "engines": {"node": "^12 || >=14"}, "vite": {"optimizeDeps": {"include": ["@ant-design/colors", "@ant-design/icons-vue", "@antfu/utils", "@element-plus/icons", "@element-plus/icons-vue", "@jeecg/online", "@vue/shared", "@vueuse/core", "@vueuse/shared", "@zxcvbn-ts/core", "ant-design-vue", "ant-design-vue/es/date-picker/locale/zh_CN", "ant-design-vue/lib/notification", "axios", "bpmn-js-token-simulation", "bpmn-js/lib/Modeler", "bpmn-js/lib/draw/Bpmn<PERSON><PERSON>er", "bpmn-js/lib/features/modeling/util/LaneUtil", "bpmn-js/lib/features/modeling/util/ModelingUtil", "bpmn-js/lib/features/palette/Palette<PERSON>", "bpmn-js/lib/util/DiUtil", "bpmn-js/lib/util/ModelUtil", "china-area-data", "clipboard", "codemirror", "codemirror-editor-vue3", "codemirror/addon/fold/brace-fold.js", "codemirror/addon/fold/comment-fold.js", "codemirror/addon/fold/foldcode.js", "codemirror/addon/fold/foldgutter.js", "codemirror/addon/fold/indent-fold.js", "codemirror/addon/hint/anyword-hint.js", "codemirror/addon/hint/show-hint.js", "codemirror/addon/selection/active-line.js", "codemirror/mode/clike/clike.js", "codemirror/mode/css/css", "codemirror/mode/css/css.js", "codemirror/mode/htmlmixed/htmlmixed", "codemirror/mode/javascript/javascript", "codemirror/mode/javascript/javascript.js", "codemirror/mode/markdown/markdown.js", "codemirror/mode/python/python.js", "codemirror/mode/r/r.js", "codemirror/mode/shell/shell.js", "codemirror/mode/sql/sql.js", "codemirror/mode/swift/swift.js", "codemirror/mode/vue/vue.js", "codemirror/mode/xml/xml.js", "cron-parser", "cropperjs", "crypto-js/aes", "crypto-js/enc-base64", "crypto-js/enc-utf8", "crypto-js/md5", "crypto-js/mode-ecb", "crypto-js/pad-pkcs7", "dayjs", "dayjs/locale/zh-cn", "diagram-js-minimap", "diagram-js/lib/util/Mouse", "dom-align", "dotenv", "echarts", "echarts/charts", "echarts/components", "echarts/core", "echarts/renderers", "element-plus", "element-plus/es/locale/lang/zh-cn", "inquirer/lib/utils/readline", "intro.js", "jsbarcode", "lodash", "lodash-es", "lodash/isEmpty", "md5", "min-dash", "moment", "moment/locale/zh-cn", "nprogress", "parse-my<PERSON>", "path-to-regexp", "pinia", "postcss", "print-js", "qrcode", "qrcodejs2", "qrcodejs2-fix", "qs", "resize-observer-polyfill", "showdown", "sortablejs", "tinymce/icons/default/icons", "tinymce/plugins/advlist", "tinymce/plugins/anchor", "tinymce/plugins/autolink", "tinymce/plugins/autosave", "tinymce/plugins/code", "tinymce/plugins/codesample", "tinymce/plugins/contextmenu", "tinymce/plugins/directionality", "tinymce/plugins/fullscreen", "tinymce/plugins/hr", "tinymce/plugins/image", "tinymce/plugins/insertdatetime", "tinymce/plugins/link", "tinymce/plugins/lists", "tinymce/plugins/media", "tinymce/plugins/nonbreaking", "tinymce/plugins/noneditable", "tinymce/plugins/pagebreak", "tinymce/plugins/paste", "tinymce/plugins/preview", "tinymce/plugins/print", "tinymce/plugins/save", "tinymce/plugins/searchreplace", "tinymce/plugins/spellchecker", "tinymce/plugins/tabfocus", "tinymce/plugins/table", "tinymce/plugins/template", "tinymce/plugins/textcolor", "tinymce/plugins/textpattern", "tinymce/plugins/visualblocks", "tinymce/plugins/visualchars", "tinymce/plugins/wordcount", "tinymce/themes/silver", "tinymce/tinymce", "vditor", "vite", "vite-plugin-theme/es/client", "vite-plugin-theme/es/colorUtils", "vue", "vue ", "vue-i18n", "vue-json-pretty", "vue-print-nb-jeecg/src/printarea", "vue-router", "vue-types", "vue/compiler-sfc", "vuedraggable", "vxe-table", "vxe-table-plugin-antd", "xe-utils", "xss"]}}}