<template>
  <div>
    <BasicModal @register="registerModal" @ok="handleOk" destroyOnClose>
      <BasicTable @register="registerTable" :rowSelection="rowSelection" :rowClassName="rowClassName"
        class="ant-table-striped">
        <template #tableTitle>
          <div class="box-title">
            <div><span class="ant-pro-table-title" style="background:  rgb(60, 240, 43) ;"></span>效期6个月内</div>
            <div><span class="ant-pro-table-title" style="background: rgb(250, 233, 157)"></span>效期3个月内</div>
            <div><span class="ant-pro-table-title" style="background:  rgb(245, 133, 133)"></span>效期1个月内(包含过期)</div>
          </div>

        </template>
        <template #lockNum="{ record }">
          <a-input @input="changeInp(record)" v-model:value="record.lockNum" :disabled="record.quantitativePackageFlag==1||record.individualFlag==1"></a-input>
        </template>

      </BasicTable>
    </BasicModal>
  </div>
</template>
<script lang="ts" setup>
import { ref, createVNode } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { useListPage } from '/@/hooks/system/useListPage'
import { BasicTable, } from '/@/components/Table';
import { MaterialColumns, schemas } from '../SpdDirectTransfer.data';
import { inventoryDetailPageList } from './DetailModal.api';
import { message, Modal } from 'ant-design-vue';
import { computed } from "vue";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
const storageId = ref()
const targetStorageId = ref()
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  storageId.value = data.sourceStorageId
  targetStorageId.value = data.targetStorageId
  checkedRows.value = []
  checkedKeys.value = [];
  setModalProps({
    confirmLoading: false,
    title: '选择物资',
    defaultFullscreen: true,
  });
});
const { tableContext } = useListPage({
  tableProps: {
    api: (params) => inventoryDetailPageList({ ...params, storageId: storageId.value,targetStorageId:targetStorageId.value,directoryFlag:1 }),
    columns: MaterialColumns,
    canResize: false,
    showIndexColumn: true,
    // showTableSetting: true,
    size: 'small',
    rowKey: 'id',
    showActionColumn: false,//隐藏操作列
    scroll: { y: 500 },
    formConfig: {
      labelWidth: 120,
      schemas: schemas,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
    },
  },
})
const [registerTable] = tableContext
/**
  * 选择列配置
  */
const checkedKeys = ref<Array<string | number>>([]);
const checkedRows = ref<Array<string | number>>([]);
const rowSelection = {
  type: 'checkbox',
  columnWidth: 50,
  selectedRowKeys: checkedKeys,
  onChange: onSelectChange,
  getCheckboxProps(record: Recordable) {
    // Demo: 第一行（id为0）的选择框禁用
    if (record.termType === 5) {
      return { disabled: true };
    } else {
      return { disabled: false };
    }
  },
};
const changeInp = (record) => {
   record.lockNum = record.lockNum.replace(/[^0-9.]/g, '');
  const num = record.lockNum.split('.');
  if (num.length > 1) {
    num[1] = num[1].slice(0, 4);
    record.lockNum = num.join('.');
    record.lockNum = record.lockNum.replace(/(\.\d*?[1-9])0+$/, '$1');
  }
  if (+record.lockNum > +record.currentNum) {
    record.lockNum = 0
    return message.warning('出库数量不能大于当前库存可用数量')
  }
}
/**
 * 选择事件
 */
function onSelectChange(selectedRowKeys: (string | number)[], selectionRows) {
  checkedKeys.value = selectedRowKeys;
  checkedRows.value = selectionRows;
}
const emit = defineEmits(['getSelectResult', 'getSelectRow', 'reset'])
function handleOk() {

  if (checkedRows.value.length == 0) { return message.warning('请选择物资') } else {
    console.log(checkedRows.value);
    let result: any = []
    checkedRows.value.forEach(item => {
      if (item.termType === 1 || item.termType === 2 || item.termType === 3 || item.termType === 4) {
        result.push(item.id)
      }
    })
    if (result.length > 0) {
      Modal.confirm({
        title: () => "提示！",
        icon: () => createVNode(ExclamationCircleOutlined),
        content: () =>
          createVNode(
            "div",
            { style: "color:#000;" },
            "当前物资中存在近效期物资，是否继续？"
          ),
        onOk() {
          emit('getSelectRow', checkedRows.value);
          emit('reset');
          closeModal();
        },
        onCancel() {
          return;
        },
      });

    } else {
      emit('getSelectRow', checkedRows.value);
      emit('reset');
      closeModal();
    }
  }

}

const rowClassName = computed(() => {
  return (record, index: number) => {
    if (record.termType === 1) {
      return 'darkGreen'
    } else if (record.termType === 2) {
      return 'darkYellow'
    } else if (record.termType === 3 || record.termType === 4 || record.termType === 5) {
      return 'darkRed'
    }
  };
})
</script>
<style scoped>
.ant-table-striped:deep(.darkGreen) td {
  background-color: rgb(60, 240, 43) !important;
}

.ant-table-striped:deep(.darkRed) td {
  background-color: rgb(245, 133, 133) !important;
}

.ant-table-striped:deep(.darkYellow) td {
  background-color: rgb(250, 233, 157) !important;
}

.ant-table-striped:deep(.box-title) {
  display: flex;
  justify-content: space-between;
}

.ant-table-striped:deep(.box-title) div {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ant-table-striped:deep(.box-title) .ant-pro-table-title {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 5px;
  margin-left: 10px;
  margin-right: 10px;
}
</style>