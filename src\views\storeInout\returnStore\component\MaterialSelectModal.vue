<template>
  <div>
    <BasicModal v-bind="$attrs" @register="registerModal" title="选择物资" :width="1300" @ok="handleOk" destroyOnClose
      :maskClosable="false">
      <BasicTable bordered size="middle" rowKey="id" :canResize="false" :columns="MaterialColumns"
        :dataSource="innerData" :pagination="true" :scroll="{ x: 1000, y: 300 }" :row-selection="rowSelection"
        :clickToRowSelect="true" >
        <template #tableTitle>
          <span class="api-select-addon"> <span style="color: red;font-size: 16px;">*</span>科室仓库:</span>
          <ApiSelect :api="() =>
            getspdStoragelist({
              column: 'storageType,simpleCode',
              order: 'asc,asd',
              storageType_MultiString: '2,3',
              delFlag: 0,
            },targetStorageId,targetStorageType)
            " v-model:value="searchData.storageId" showSearch placeholder="请选择科室仓库" optionFilterProp="storageNameAbbr"
            labelField="storageName" valueField="id" style="width: 200px">
          </ApiSelect>
          <a-input v-model:value="searchData.goodsCode" style="width: 300px" placeholder="请输入物资编码"
            @keyup.enter="addRow">
            <template #addonBefore>
              <span>物资编码：</span>
            </template>
          </a-input>
          <a-input v-model:value="searchData.goodsName" style="width: 300px" placeholder="请输入名称" @keyup.enter="addRow">
            <template #addonBefore>
              <span>物资名称：</span>
            </template>
          </a-input>
          <a-input v-model:value="searchData.goodsSpecs" style="width: 300px" placeholder="请输入规格" @keyup.enter="addRow">
            <template #addonBefore>
              <span>规格型号：</span>
            </template>
          </a-input>
          <a-button type="primary" preIcon="ant-design:search-outlined" @click="search">查询</a-button>
          <a-button type="primary" preIcon="ant-design:reload-outlined" @click="resets">重置</a-button>
        </template>
                       <template #outNum="{ record }">
          <a-input @input="changeInp(record)" v-model:value="record.outNum"
            :disabled="record.quantitativePackageFlag == 1 || record.individualFlag == 1"></a-input>
        </template>
      </BasicTable>
    </BasicModal>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, defineEmits } from "vue";
import { BasicModal, useModalInner } from "/@/components/Modal";
import { BasicTable, TableAction } from "/@/components/Table";
import { MaterialColumns } from "../SpdDirectTransfer.data";
import { inventoryDetailPageList } from "../SpdDirectTransfer.api";
import { message } from "ant-design-vue";
import { getspdStoragelist } from "../SpdDirectTransfer.api";
import { ApiSelect } from "/@/components/Form/index";
/** */
const innerData = ref<any[]>([]);
let searchData = reactive({
  goodsCode: "",
  goodsName: "",
  goodsSpecs: "",
  storageId: "",
});
const targetStorageId = ref("");
const targetStorageType = ref("");
//赋值
const [registerModal, { closeModal }] = useModalInner(async (_data) => {
  searchData.goodsCode = "";
  searchData.goodsName = "";
  searchData.goodsSpecs = "";
  searchData.storageId = "";
  innerData.value = [];
  checkedRows.value = [];
  checkedKeys.value = [];
  targetStorageId.value = _data.targetStorageId;
  targetStorageType.value = _data.targetStorageType;
});

const search = async () => {
  innerData.value = []
  if (searchData.storageId == "") {
    return message.warning("请先选择科室仓库");
  } else {
    let data: any = JSON.parse(JSON.stringify(searchData));
    data.operationFlag = 1;
    data.quantitativePackageQueryFlag=1
    for (const key in data) {
      if (data[key] && key != "storageId" && key != "operationFlag"&& key != "quantitativePackageQueryFlag") {
        data[key] = "*" + data[key] + "*";
      }
    }
    const res = await inventoryDetailPageList(data);
    innerData.value = res;
  }

};
const resets = () => {
  searchData.goodsCode = "";
  searchData.goodsName = "";
  searchData.goodsSpecs = "";
  innerData.value = [];
  checkedRows.value = [];
};
/**
 * 选择列配置
 */
const checkedKeys = ref<Array<string | number>>([]);
const checkedRows = ref<Array<string | number>>([]);
const rowSelection: any = {
  type: "checkbox",
  columnWidth: 50,
  selectedRowKeys: checkedKeys,
  onChange: onSelectChange,
};
/**
 * 选择事件
 */
function onSelectChange(selectedRowKeys: (string | number)[], selectionRows) {
  checkedKeys.value = selectedRowKeys;
  checkedRows.value = selectionRows;
}
async function addRow() {
  search()
}

// 编辑事件
const changeInp = (record) => {
  record.outNum = record.outNum.replace(/[^0-9.]/g, '');
  const num = record.outNum.split('.');
  if (num.length > 1) {
    num[1] = num[1].slice(0, 4);
    record.outNum = num.join('.');
    record.outNum = record.outNum.replace(/(\.\d*?[1-9])0+$/, '$1');
  }
  if (+record.outNum > +record.currentNum) {
    record.outNum =record.currentNum;
    return message.warning('出库数量不能大于当前库存数量');
  }
};
/**
 *
 */
const emit = defineEmits(["getSelectResult", "getSelectRow", "reset"]);
function handleOk() {
  if (checkedRows.value.length == 0) {
    return message.warning("请选择物资");
  }
  emit("getSelectRow", checkedRows.value);
  emit("reset");
  //关闭弹窗
  closeModal();
}
</script>
<style scoped lang="scss">
.api-select-addon {
  margin: 0 0 4px 0;
  position: relative;
  padding: 0 11px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: normal;
  font-size: 14px;
  text-align: center;
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 2px 0 0 2px;
  transition: all 0.3s;
}
</style>
