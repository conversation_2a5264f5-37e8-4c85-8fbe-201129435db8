<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
       <!--插槽:table标题-->
       <template #tableTitle>
        <a-button type="primary" @click="handleAdd" v-auth="'add-operation-scheduling'">新建手术排班</a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <NewOperation @register="registerModalNew" @success="handleSuccess"></NewOperation>
    <!--申领 -->
    <OperationNewModel
      @register="registerNewModal"
      @success="handleSuccess"
    ></OperationNewModel>
  </div>
</template>

<script lang="ts" name="operationGrant-OperationMineClasses" setup>
import { BasicTable, TableAction } from "/@/components/Table";
import { useModal } from "/@/components/Modal";
import { useListPage } from "/@/hooks/system/useListPage";
import NewOperation from "./components/NewOperation/index.vue";
import OperationNewModel from "../MperationMedicalApply/components/ReplenishOrAdd/replenish.vue";
import { columns, searchFormSchema } from "./index.data";
import { list } from "./index.api";
//注册model
const [registerModalNew, { openModal: openModalNew }] = useModal();
const [registerNewModal, { openModal: openModal }] = useModal();
//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    api: list,
    columns,
    canResize: false,
    scroll: { y: 480 },
    showIndexColumn: true,
    size: "small",
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToNumber: [],
      fieldMapToTime: [
        ["operationTime", ["operationTime_begin", "operationTime_end"], "YYYY-MM-DD"],
      ],
    },
    actionColumn: {
      width: 120,
      fixed: "right",
    },
    // 请求之前对参数做处理
    beforeFetch(params) {
      params.scheduleFlag = 0;
    },
  },
});

const [registerTable, { reload }, { selectedRowKeys }] = tableContext;
/**
 * 申领事件
 */
function handleApply(record) {
  openModal(true, {
    record,
    isUpdate: false,
    showFooter: false,
    isClass: false,
    isSupplement:0,
  });
}
/**
 * 补领事件
 */
function handleReplenish(record) {
  openModal(true, {
    record,
    isUpdate: false,
    showFooter: false,
    isClass: false,
    isSupplement:1,
  });
}
// 新增手术排班
function handleAdd() {
  openModalNew(true, {
    isUpdate: false,
    showFooter: false,
  });
}
/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}
/**
 * 操作栏
 */
function getTableAction(record: any) {
  return [
    {
      label: "申领物资",
      onClick: handleApply.bind(null, record),
      ifShow: (_action) => {
        return record.applyStatus != 1; // 根据状态是否显示
      },
    },
    {
      label: "补领物资",
      onClick: handleReplenish.bind(null, record),
      ifShow: (_action) => {
        return record.applyStatus == 1; // 根据状态是否显示
      },
    },
  ];
}
</script>

<style scoped>
</style>
