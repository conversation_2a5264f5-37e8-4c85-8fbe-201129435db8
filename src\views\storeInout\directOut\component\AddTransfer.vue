<template>
  <div>
    <BasicModal v-bind="$attrs" @register="registerModal" title="调拨出库" @ok="handleSubmit" :dragHandle="null" :maskClosable="false" :defaultFullscreen="true">
      <BasicTable
        bordered
        rowKey="id"
        :action-column="actionColumn"
        :columns="planColumns"
        :dataSource="innerData"
        size="small"
        :scroll="{ x: 1000, y: 500 }"
        :pagination="false"
        :rowClassName="rowClassName"
        class="ant-table-striped"
      >
        <template #tableTitle>
          <BasicForm @register="register" @submit="openModals">
            <template #code="{ model, field }">
              <a-input v-model:value="model[field]" ref="inputRef" @keyup.enter="addRow" placeholder="请输入条码" style="width: 400px" allowClear />
            </template>
            <template #applyUser="{ model, field }">
              <a-select allowClear v-model:value="model[field]" show-search :filterOption="filterOption" placeholder="请选择领用人" style="width: 400px" :options="applyerList" />
            </template>
            <template #targetStorage="{ model, field }">
              <ApiSelect
                :api="() => getspdStoragelist({ delFlag: 0, storageType_MultiString: '2,3' })"
                :immediate="false"
                v-model:value="model[field]"
                showSearch
                placeholder="请选择目标库房"
                allowClear
                optionFilterProp="storageNameAbbr"
                labelField="storageName"
                valueField="id"
                @change="changeTargetStore"
                style="width: 400px"
              >
              </ApiSelect>
            </template>
            <template #sourceStorageId="{ model, field }">
              <ApiSelect
                :api="() => getspdStoragelist({ delFlag: 0, storageType_MultiString: '1,2' })"
                :immediate="true"
                @click="handleClick"
                @change="handleChange"
                v-model:value="model[field]"
                showSearch
                placeholder="请选择出库仓库"
                allowClear
                optionFilterProp="storageNameAbbr"
                labelField="storageName"
                valueField="id"
                style="width: 400px"
              >
              </ApiSelect>
            </template>
          </BasicForm>
          <p class="note">注：红色标记物资为当前物资存在更近效期物资</p>
        </template>
        <template #lockNum="{ record }">
          <a-input
            @input="changeInpNum(record)"
            id="inputNumber"
            v-model:value="record.lockNum"
            :min="0"
            :max="10000000"
            :disabled="record.quantitativePackageFlag == 1 || record.individualFlag === 1"
          />
        </template>
        <template #action="{ record }">
          <TableAction
            :actions="[
              {
                label: '删除',
                icon: 'ic:outline-delete-outline',
                onClick: handleDel.bind(null, record),
              },
            ]"
          />
        </template>
      </BasicTable>
      <template #insertFooter>
        <span style="padding-right: 20px">是否推送</span>
        <a-switch checked-children="是" un-checked-children="否" v-model:checked="subForm.pushFlag" />
      </template>
    </BasicModal>
    <MaterialSelectModal @register="regModal" @getSelectRow="setValue"></MaterialSelectModal>
  </div>
</template>
<script lang="ts" setup>
import { BasicModal, useModalInner } from '/@/components/Modal';
import { useModal } from '/@/components/Modal';
import { BasicForm, FormSchema, useForm } from '/@/components/Form/index';
import { ApiSelect } from '/@/components/Form/index';
import MaterialSelectModal from './MaterialSelectModal.vue';
import { ref, reactive, createVNode, computed } from 'vue';
import { getspdStoragelist } from '/@/api/common/api';
import { BasicTable, TableAction } from '/@/components/Table';
import { planColumns } from '../SpdDirectTransfer.data';
import { outStore, queryByStorage } from '../SpdDirectTransfer.api';
import { inventoryDetailPageList } from '/@/api/common/api';
import { useMessage } from '/@/hooks/web/useMessage';
import { Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
const { createMessage, createConfirm } = useMessage();
import { useUserStore } from '/@/store/modules/user';
const { hospitalZoneInfo } = useUserStore();

const innerData = ref<any[]>([]);
const showFooterFlg = ref(true);
const isUpdate = ref(true);
const actionColumn = {
  width: 100,
  title: '操作',
  dataIndex: 'action',
  slots: { customRender: 'action' },
  fixed: 'right',
};
const emit = defineEmits(['success']);
const inputRef = ref();

//注册model
const [regModal, { openModal }] = useModal();
const openModals = (record) => {
  if (!getFieldsValue().targetStorageId) return createMessage.warning('请选择目标仓库');
  if (!getFieldsValue().sourceStorageId) return createMessage.warning('请选择出库仓库');
  openModal(true, {
    record,
    sourceStorageId: getFieldsValue().sourceStorageId,
    targetStorageId: getFieldsValue().targetStorageId,
    isUpdate: false,
    showFooter: false,
  });
};
//表单赋值
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  clearValidate();
  resetFields();
  innerData.value = [];
  subForm.detailList = [];
  applyerList.value = [];
  arr.value = [];
  setModalProps({
    confirmLoading: false,
    showCancelBtn: !!data?.showFooter,
    showOkBtn: !!data?.showFooter,
    width: 1700,
  });
  isUpdate.value = !!data?.isUpdate;
  showFooterFlg.value = data?.showFooter;
});
const schemas: FormSchema[] = [
  {
    field: 'code',
    component: 'Select',
    label: '物资条码',
    slot: 'code',
    colProps: {
      span: 8,
    },
  },
  {
    field: 'applyUser',
    component: 'Select',
    label: '领用人',
    slot: 'applyUser',
    colProps: {
      span: 8,
    },
  },
  {
    field: 'remark',
    component: 'Input',
    label: '领用人备注',
    colProps: {
      span: 8,
    },
  },
  {
    label: '目标库房',
    field: 'targetStorageId',
    component: 'ApiSelect',
    rules: [{ required: true, message: '请选择目标库房', trigger: ['change', 'blur'] }],
    slot: 'targetStorage',
    colProps: {
      span: 8,
    },
  },
  {
    label: '出库仓库',
    field: 'sourceStorageId',
    component: 'ApiSelect',
    rules: [{ required: true, message: '请选择出库仓库', trigger: ['change', 'blur'] }],
    defaultValue: hospitalZoneInfo?.storage?.id,
    slot: 'sourceStorageId',
    colProps: {
      span: 8,
    },
  },
];
const [register, { validateFields, clearValidate, getFieldsValue, resetFields, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  submitButtonOptions: { text: '选择物资', preIcon: undefined },
  actionColOptions: { span: 3 },
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    order: 4,
  },
  showResetButton: false,
  schemas,
});
/**
 * 返回勾选数据
 */
const setValue = async (options) => {
  options.forEach((item) => {
    let arr = innerData.value.filter((v) => v.id == item.id);
    if (item.individualFlag === 1) {
      if (!arr.length) {
        item.lockNum = 1;
        innerData.value.unshift(item);
      }
    } else {
      if (arr.length) {
        arr[0]['lockNum'] = +arr[0]['lockNum'] + +item.lockNum;
      } else {
        innerData.value.unshift(item);
      }
    }
  });
  await getTermSort(innerData.value);
};

function getTermSort(list) {
  const groupedArray = groupByProperty(list, 'goodsCode');
  groupedArray.forEach((item) => {
    let num: any = findMissingElements(item)[0] ? findMissingElements(item)[0] : 1;
    console.log(item, num);
    item.sort((a, b) => { return a.termSort - b.termSort; });
    const arr = groupByProperty(item, 'termSort');
    console.log(arr);
    let counterBool=false
    arr.forEach((item) => {
      for (let i = 0; i < item.length; i++) {
        if (item[i].termSort <= num && counterBool==false) {
          item[i].termSortBool = false;
        } else {
          item[i].termSortBool = true;
        }
      }
      if(item.length==item[0].termCurrentNum){
        counterBool=false
      }else{
        counterBool=true
      }
    });
  });
}
function groupByProperty(arr, property) {
  const map = new Map();
  arr.forEach((item) => {
    const key = item[property];
    if (!map.has(key)) {
      map.set(key, []);
    }
    map.get(key).push(item);
  });

  return Array.from(map.values());
}
function findMissingElements(arr) {
  if (arr.length === 0) return [];
  // 提取并排序num属性
  const nums = arr.map((item) => item.termSort).sort((a, b) => a - b);
  const missing = [];
  // 始终从1开始
  const min = 1;
  const max = nums[nums.length - 1];
  // 查找缺失的数字（从1开始）
  for (let i = min; i <= max; i++) {
    if (!nums.includes(i)) {
      missing.push(i);
    }
  }
  // 如果没有缺失的元素，返回最后一个值+1
  if (missing.length === 0 && nums.length > 0) {
    return [nums[nums.length - 1] + 1];
  }

  return missing;
}

const subForm = reactive<any>({
  code: '',
  pushFlag: true,
  detailList: [],
});
// 扫码匹配
const addRow = async (e) => {
  if (!getFieldsValue().targetStorageId) return createMessage.warning('请选择目标仓库');
  if (!getFieldsValue().sourceStorageId) return createMessage.warning('请选择出库仓库');
  if (!getFieldsValue().code) return;
  try {
    const res = await inventoryDetailPageList({ code: getFieldsValue().code, storageId: getFieldsValue().sourceStorageId, targetStorageId: getFieldsValue().targetStorageId, directoryFlag: 1 });
    let term = new Date(res[0]?.term);
    let daysNum = (term.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24);
    if (res.length && daysNum <= 180 && daysNum > 0) {
      Modal.confirm({
        title: () => '提示！',
        icon: () => createVNode(ExclamationCircleOutlined),
        content: () => createVNode('div', { style: 'color:#000;' }, '该物资效期剩余' + Math.floor(daysNum) + '天，是否继续？'),
        onOk() {
            // 根据定数包标志设置数量
          res[0].lockNum = res[0].quantitativePackageFlag === 1 ? res[0].quantitativePackageNum : 1;
          
          // 设置仓库ID
          res[0].storeId = res[0].id;
          
          // 初始化现有数据变量
          let existingData = '';
          
          // 根据定数包标志查找现有数据
          if (res[0].quantitativePackageFlag === 0) {
            existingData = innerData.value.find((data) => data.uniqueCode === res[0].uniqueCode);
          } else {
            existingData = innerData.value.find((data) => data.uniqueCode === res[0].uniqueCode && data.id === res[0].id);
          }
          
          // 检查是否有重复的条形码
          if (existingData) {
            createMessage.warning('条形码已存在,请勿重复添加');
          } else {
            // 将新数据添加到列表开头
            innerData.value.unshift(res[0]);
            // 显示成功消息
            createMessage.success('扫描成功');
          }
          inputRef.value.focus();
        },
        onCancel() {
          inputRef.value.focus();
        },
      });
    } else if (!res.length) {
      createMessage.warning('条形码不存在');
    } else if (daysNum <= 0) {
      createMessage.error('物资已过期');
    } else {
      // 检查数据是否已存在
      res[0].lockNum = res[0].quantitativePackageFlag === 1 ? res[0].quantitativePackageNum : 1;
      res[0].storeId = res[0].id;
      let existingData = '';
      if (res[0].quantitativePackageFlag === 0) {
        existingData = innerData.value.find((data) => data.id === res[0].id);
      } else {
        existingData = innerData.value.find((data) => data.id === res[0].id);
      }
      if (existingData) {
        createMessage.warning('条形码已存在,请勿重复添加');
      } else {
        // 添加数据到表格
        innerData.value.unshift(res[0]);
        createMessage.success('扫描成功');
      }
    }
  } catch (error) {
    console.log(error);
  } finally {
    setFieldsValue({ code: undefined });
    getTermSort(innerData.value);
  }
};

//表单提交事件
let arr = ref<any>([]);
async function handleSubmit() {
  try {
    arr.value = innerData.value.reduce((acc, item) => {
      let existingItem = acc.find((obj) => obj.inventoryDetailId === item.inventoryDetailId);
      if (item.inventoryDetailId !== null) {
        if (!existingItem) {
          existingItem = {
            inventoryDetailId: item.inventoryDetailId,
            inventoryDetailNum: 0,
            inventoryRecordIds: [],
            goodsCode: item.goodsCode,
          };
          acc.push(existingItem);
        }
        existingItem.inventoryDetailNum++;
        existingItem.inventoryRecordIds.push(item.id);
      } else {
        acc.push({
          inventoryDetailId: item.id,
          inventoryDetailNum: Number(item.lockNum),
          goodsCode: item.goodsCode,
        });
      }

      return acc;
    }, []);
    arr.value.forEach((item) => {
      item.inventoryRecordIds = [...new Set(item.inventoryRecordIds)];
    });
    let obj = {
      ...getFieldsValue(),
      detailList: arr.value,
      pushFlag: +subForm.pushFlag,
    };
    setModalProps({ confirmLoading: true });
    await outStore(obj);
    closeModal();
    emit('success');
  } catch (e) {
    console.log(e, 'e');
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
const handleDel = (record: Recordable) => {
  const index = innerData.value.findIndex((item) => item.id === record.id);
  innerData.value.splice(index, 1);
  arr.value = [];
  let list: any[] = [];
  innerData.value.forEach((item) => {
    if (item.goodsCode == record.goodsCode) {
      list.push(item);
    }
  });
  let num: any = findMissingElements(list)[0] ? findMissingElements(list)[0] : 0;
  list.forEach((item) => {
    if (item.termSort <= num) {
      item.termSortBool = false;
    } else if (item.termSort > num) {
      item.termSortBool = true;
    }
  });
};
const oldStorageId = ref();
function handleChange() {
  if (!innerData.value.length) return;
  createConfirm({
    iconType: 'warning',
    title: '确认修改出库仓库',
    content: '修改出库仓库将清空物资列表，确认是否切换？',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      innerData.value = [];
    },
    onCancel: () => {
      setFieldsValue({ sourceStorageId: oldStorageId.value });
    },
  });
}
// 目标库房change事件获取申领人列表
const applyerList = ref();
const changeTargetStore = async (e) => {
  const res = await queryByStorage({ storageId: e });
  applyerList.value = res.map((item) => {
    return {
      label: `${item.realname + `(${item.username})`}`,
      key: item.username,
      value: item.username,
      values: `${item.realname + `(${item.username})`}`,
    };
  });
  //setFieldsValue({ applyUser: res[0]?.username })
};
const filterOption = (input: string, option: any) => {
  return option.values.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
const handleClick = () => {
  oldStorageId.value = getFieldsValue().sourceStorageId;
};
const changeInpNum = (record) => {
  record.lockNum = record.lockNum.replace(/[^0-9.]/g, '');
  const num = record.lockNum.split('.');
  if (num.length > 1) {
    num[1] = num[1].slice(0, 4);
    record.lockNum = num.join('.');
    record.lockNum = record.lockNum.replace(/(\.\d*?[1-9])0+$/, '$1');
  }
  if (+record.lockNum > +record.currentNum) {
    createMessage.warning('出库数量不能大于当前库存可用数量');
    record.lockNum = 0;
  }
};
const rowClassName = computed(() => {
  return (record, index: number) => {
    if (record.termSortBool === true) {
      return 'darkRed';
    }
  };
});
</script>
<style scoped lang="scss">
.jeecg-basic-table-header__tableTitle {
  display: contents;
}

.note {
  width: 300px;
  color: red;
  margin-left: 85%;
}

.ant-table-striped:deep(.darkRed) td {
  color: red !important;
}
</style>
