<template>
    <BasicModal v-bind="$attrs" @register="registerModal" @ok="ok" destroyOnClose width="1400" :maskClosable="false" :default-fullscreen="true"> 
        <BasicTable @register="registerTable" :rowSelection="rowSelection">
            <template #billTotalAmt="{ record }">
                {{ currentAmt(record).toFixed(2) }}
            </template>
        </BasicTable>
    </BasicModal>
</template>

<script setup lang="ts">
import { defineEmits } from "vue";
import { BasicTable, } from "/@/components/Table";
import { inventoryPackageList } from "../index.api";
import { BasicModal, useModalInner } from "/@/components/Modal";
import { lowPackagesColumns, lowPacksgesFormSchema } from "../index.data";
import { useUserStore } from "/@/store/modules/user";
import { useListPage } from "/@/hooks/system/useListPage";
const userStore = useUserStore();
const emit = defineEmits(["getSelectResult", "getSelectRow", "reset"]);
const currentAmt = (record) =>
    record.currentOutStoreNum * record.goodsPrice
        ? record.currentOutStoreNum * record.goodsPrice
        : 0;

//注册tableA
const { tableContext } = useListPage({
    tableProps: {
        api: inventoryPackageList,
        columns: lowPackagesColumns,
        clickToRowSelect: true,
        rowKey: "id",
        bordered: true,
        canResize: false,
        scroll: { y: 500 },
        size: "small",
        showIndexColumn: true,
        formConfig: {
            labelWidth: 100,
            baseColProps: {
                xs: 24,
                sm: 8,
                md: 6,
                lg: 8,
                xl: 6,
                xxl: 6,
            },
            schemas: lowPacksgesFormSchema,
            autoSubmitOnEnter: true,
            showAdvancedButton: false,
            fieldMapToNumber: [],
            fieldMapToTime: [
                ["acceptDate", ["acceptDate_begin", "acceptDate_end"], "YYYY-MM-DD"],
            ],
        },
        showActionColumn: false,
        beforeFetch(params) {
            params.storageId = userStore.hospitalZoneInfo?.storage.id
            params.barcodeType = '2'
            params.acceptDate_begin =
                params.acceptDate_begin == null ? null : params.acceptDate_begin + " 00:00:00";
            params.acceptDate_end =
                params.acceptDate_end == null ? null : params.acceptDate_end + " 23:59:59";
        },
    },
});
const [registerTable, { setSelectedRowKeys, }, { rowSelection, selectedRows }] = tableContext

let keysArr: any = []
let rowsArr: any = []
//注册model
const [registerModal, { setModalProps, closeModal,changeLoading,changeOkLoading }] = useModalInner((data) => {
    rowsArr = []
    keysArr = []
    let option = [...data.record]
    option.forEach((item) => {
        item = JSON.parse(JSON.stringify(item))
        if (item.quantitativePackageFlag == 1) {
            keysArr.push(item.id)
            rowsArr.push(item)
        }
    });

    setSelectedRowKeys(keysArr)
    setModalProps({
        width: 1400,
        title: "选择定数包",
        okText: "确认",
    });
});

const ok = async () => {
    changeOkLoading(true);
    changeLoading(true)
    emit("getSelectRow", { data: selectedRows.value.length > 0 ? selectedRows.value : rowsArr, quantitativePackageFlag: 1 });
    //关闭弹窗
    closeModal();
    changeOkLoading(false);
    changeLoading(false)
};
</script>

<style lang="scss" scoped>
</style>