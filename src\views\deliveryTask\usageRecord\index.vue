<template>
  <div>
    <BasicTable 
      @register="registerTable"   
      class="ant-table-striped"
      :rowClassName="rowClassName">
          <template #tableTitle>
          <div class="box-title">
            <div><span class="ant-pro-table-title" style="background:  rgba(227, 238, 134, 0.507);"></span>未经过结算台发放物资</div>
          </div>

        </template>
          <template #footer="currentPageData">
        <div class="foot pd6 dpflex jcsb">
          <div>合计</div>
          <div class="foot-total">{{ getFootTotal(currentPageData) }}</div>
        </div>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" name="deliveryTask-usageRecord" setup>

import { BasicTable } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage'
import { columns, searchFormSchema } from './index.data';
import { list } from './index.api';
import { computed } from 'vue';
import { getNumToThousands } from "/@/utils/index";




//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    api: list,
    columns,
    canResize: false,
    ellipsis: true,
    scroll:{y:500},
    showIndexColumn: true,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToNumber: [
      ],
      fieldMapToTime: [
        ['createTime', ['createTime_begin', 'createTime_end'], 'YYYY-MM-DD']
      ],
    },
    showActionColumn: false,
    beforeFetch(params) {

    }
  },
})
const [registerTable] = tableContext

const rowClassName = computed(() => {
  return (record, index: number) => (record.operationSchedulingCode == '' ? "darkYellow" : "");
});
const getFootTotal = (currentPageData) =>
  `${handleSummary(currentPageData).totalNum} ,\xa0` +
  `${handleSummary(currentPageData).totalAmount}`;
function handleSummary(tableData: Recordable[]) {
  // 金额合计
  const totalAmt = tableData.reduce((prev, next) => {
    prev += Number(next.totalAmt);
    return prev;
  }, 0);
  // 数量总计
  const totalNumber = tableData.reduce((prev, next) => {
    prev += Number(next.totalNum);
    return prev;
  }, 0);
  return {
    totalAmount: `本页总金额 : ${getNumToThousands(totalAmt)}`,
    totalNum: `本页总数量 : ${totalNumber.toFixed(2)}`,
  };
}

</script>
<style scoped>
/*:deep(.ant-col-4) {*/
  /*margin-left: 75%;*/
/*}*/

.ant-table-striped :deep(.darkYellow) td {
  background-color: rgba(227, 238, 134, 0.507);
}
.ant-table-striped:deep(.box-title) {
  display: flex;
  justify-content: space-between;
}

.ant-table-striped:deep(.box-title) div {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ant-table-striped:deep(.box-title) .ant-pro-table-title {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 5px;
  margin-left: 10px;
  margin-right: 10px;

}
</style>
