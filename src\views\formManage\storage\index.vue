<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
      <template #fileSlot="{ text }">
        <span v-if="!text" style="font-size: 12px; font-style: italic">无文件</span>
        <a-button v-else :ghost="true" type="primary" preIcon="ant-design:download-outlined" size="small"
          @click="downloadFile(text)">下载</a-button>
      </template>
      <template #footer="currentPageData">
        <div class="foot pd6 dpflex jcsb">
          <div>合计</div>
          <div class="dpflex">
            <div class="ml100">{{ `${checkSumAmount}` }}</div>
            <div class="foot-total">{{ getFootTotal(currentPageData) }}</div>
        </div>
      </div>
    </template>
    </BasicTable>
  </div>
  <SpdStorageModal @register="registerModal" @success="handleSuccess"></SpdStorageModal>
</template>

<script lang="ts" name="spd.business-spdDevice" setup>
import { BasicTable, TableAction } from "/@/components/Table";
import { useModal } from "/@/components/Modal";
import { useListPage } from "/@/hooks/system/useListPage";
import SpdStorageModal from "./components/SpdStorageModal.vue";
import { getspdStoragelist } from '/@/api/common/api';
import { columns, searchFormSchema } from "./SpdStorage.data";
import { list, getImportUrl, getExportUrl, getDeliveryspdStoragelist } from "./SpdStorage.api";
import { downloadFile } from "/@/utils/common/renderUtils";
import { onMounted, computed } from "vue";
import { useUserStore } from '/@/store/modules/user';
import { handleSummaryNew, checkedSum } from '/@/utils/index'
const getFootTotal = (currentPageData)=> handleSummaryNew(currentPageData,'totalAmt','currentNum')
const checkSumAmount = computed(() => checkedSum(selectedRows.value,'totalAmt','currentNum')) // 勾选合计
const userStore: any = useUserStore();

const code = (userStore.hospitalZoneInfo?.depart?.orgCode);
const storageId = (userStore.hospitalZoneInfo?.storage?.id);

const props = defineProps({
  formtype: {
    type: String,
    default: "0",
  },
});


onMounted(() => {
  console.log("mounted",props.formtype);
  
  if (props.formtype == '1') {
    getForm().updateSchema({
      label: '仓库',
      field: 'storageId',
      component: 'ApiSelect',
      componentProps: {
        api: () => getspdStoragelist({ column: 'storageType', order: 'asc' }),
        showSearch: true,
        optionFilterProp: 'storageNameAbbr',
        labelField: 'storageName',
        valueField: 'id',
      },
      defaultValue: storageId,
    })

  } else if (props.formtype == '2') {
    getForm().updateSchema({
      label: '仓库',
      field: 'storageId',
      component: 'ApiSelect',
      componentProps: {
        api: () => getDeliveryspdStoragelist({ departCode: code }),
        showSearch: true,
        optionFilterProp: 'storageNameAbbr',
        labelField: 'storageName',
        valueField: 'id',
      },
      defaultValue: storageId,
      required: true,
    })
  }
});


//注册model
const [registerModal, { openModal }] = useModal();

//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    title: "仓库库存耗材",
    api: list,
    columns,
    canResize: false,
    scroll: { y: 480 },
    showIndexColumn: true,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToNumber: [],
      fieldMapToTime: [],
    },
    actionColumn: {
      width: 120,
      fixed: "right",
    },
    // 请求之前对参数做处理
    beforeFetch(params) {
      params.adminFlag = props.formtype == "0" ? true : false;
    },
  },
  exportConfig: {
    name: "仓库",
    url: getExportUrl,
  },
  importConfig: {
    url: getImportUrl,
    success: handleSuccess,
  },
});

const [registerTable, { reload, getForm }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;

/**
 * 详情
 */
function handleDetail(record: Recordable) {
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}
/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: "详情",
      onClick: handleDetail.bind(null, record),
    },
  ];
}
/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}
</script>

<style scoped>
</style>
