<template>
  <div>
    <iframe style="display: none" ref="iframeRef" :srcdoc="printContainer" frameborder="0"></iframe>
    <div id="print" style="display: none">
      <div class="print-outer" v-for="(item, index) in list" :key="item.id" style="page-break-after:always;">
        <h1 style="text-align: center;font-size: 20px;">首都医科大学附属北京{{ title }}科室申领单</h1>
        <div class="mt-4" style=" text-align: left;width: 100%;display: flex; ">
          <span style="width: 30%;">申领单号: {{ item.applyOrderNo }}</span>
          <span style="width: 30%;">申领科室: {{ item.departName }}</span>
          <span style="width: 30%;">申领人: {{ item.applyUserRealname }}</span>
        </div>
        <div class="mt-4" style="text-align: left;width: 100%;display: flex;">
          <span style="width: 30%;"> 申领日期: {{ item.applyCreateTime.length>10?item.applyCreateTime.substr(0,10):item.applyCreateTime}}</span>
          <span style="width: 30%;">科室地址: {{ item.deliveryAddress }}</span>
          <span style="width: 25%;">审核状态: {{ item.auditStatus_dictText }}</span>
        </div>
        <!-- <div class="imgContainers" style="position: absolute;right: 0px;top:0px">
          <barcode-print :key="item + new Date()" :value="item.transferNo" :height="50" :displayValue="false">
          </barcode-print>
        </div> -->

        <!-- <div style="border: 0.5px solid #474747;width: 100%;"></div> -->
        <a-table style="height: 450px;" :dataSource="item.detailList" :columns="columns" :pagination="false">
          <template #goodsSpecsDetail="{ record }">
            <span style="color:#000">{{ getSpecs(record) }}</span>
          </template>
        </a-table>
        <div v-if="item.totalPage == item.currentPage" style="border: 0.5px solid #474747;width: 100%;"></div>
        <div v-if="item.totalPage == item.currentPage" class="mt-6" style="display: flex;">
          <span style="width: 70%;">本单合计</span>
          <span style="width: 15%;">数量:{{ item.allPageTotalNum }}</span>
          <span style="width: 15%;">金额:{{ item.allPageTotalAmt }}</span>
        </div>
        <div style="border: 0.5px solid #474747;width: 100%;"></div>
        <!-- <div class="mt-4" style="display: flex;">
          <span style="width: 25%;">库管员:</span>
          <span style="width: 25%;">配送员:</span>
          <span style="width: 25%;">科室收货人:</span>
          <span style="width: 25%;">审核:</span>
        </div> -->
        <div class="mt-6" style="text-align: center; ">
          <span>—— {{ item.currentPage }}/{{ item.totalPage }} ——</span>
        </div>
      </div>
    </div>
  </div>
</template>
 
<script setup >
import { ref, onMounted, nextTick, watch, defineProps } from 'vue';
import { useUserStore } from '/@/store/modules/user';
import { getSpecs } from '/@/utils/index';
// import QRCode from 'qrcodejs2-fix';
const userStore = useUserStore()
let title = ref(userStore.hospitalZoneInfo.comp.name)
const generateCode = () => {
  //document.getElementById("QR_code").innerHTML = "";
  // setTimeout(() => {
  // new QRCode(document.getElementById("QR_code"), {
  //   // text: tableData.transferNo
  //   text: 'https://www.baidu.com/',
  //   width: 60,
  //   height: 60
  // });
  // }, 1000)

};
const props = defineProps({
  value: {
    type: String,
    default: '',
  },
  list: {
    type: Array,
    default: [],
  },
});
const columns = [
  {
    title: "序号",
    dataIndex: 'sortNum',
    width: 25
  },
  {
    title: '材料编码',
    dataIndex: 'goodsCode',
    width: 125
  },
  {
    title: '材料名称',
    dataIndex: 'goodsName',
    width: 200
  },
  {
    title: '规格型号',
    dataIndex: 'goodsSpecsDetail',
    width: 200,
    slots: { customRender: 'goodsSpecsDetail' },
  },
  {
    title: '单位',
    dataIndex: 'unitName',
    width: 40
  },
  {
    title: '数量',
    dataIndex: 'applyNum',
    width: 40
  },
  {
    title: '单价',
    dataIndex: 'goodsPrice',
    width: 80
  },
  {
    title: '金额(元)',
    dataIndex: 'totalAmt',
    width: 80
  },
  {
    title: '响应仓库',
    dataIndex: 'outStorageName',
    width: 80
  },
]
const printContainer = ref('');
const iframeRef = ref(null);
function print() {
  //赋值
  if (!iframeRef.value) {
    console.error("iframeRef is not set or invalid.");
    return;
  }
  printContainer.value = document.getElementById('print').innerHTML;
  // 获取 iframe 内部的文档对象
  const iframeDoc = iframeRef.value.contentDocument || iframeRef.value.contentWindow.document;
  // 创建并设置样式
  const style = iframeDoc.createElement('style');
  style.innerHTML = `
  @media print {
      /* 重置打印样式 */
      * {
        margin:  0!important;
        padding: 0!important;
        box-sizing: border-box !important;
        font-size: 12px !important;
      }
      /* 设置打印表格的样式 */
      table {
          width: 100%;
          border-collapse: collapse;
      }
      tr{
        height:47px;
      }
      th, td {
        text-align: center; /* 默认让文本居中对齐 */
      }
      /* 设置表格内容的最大行数 目前未生效 */
      .td {
          max-height: 2em !important;
          white-space: pre-wrap !important;
          word-break: break-all !important;
          overflow: hidden !important;
      }
      h1{
        font-size:16px !important;
        font-weight: 600 !important;
      }
      .mt-4{
        margin-top: 4px !important;
      }
      .mt-6{
        margin-top: 6px !important;
      }
      .print-outer{
        position:relative;
      }
      /* 添加自定义打印样式 */
      @page {
        size:148mm 210mm portrait; // landscape 横屏 portrait 竖屏
        // margin: 8mm !important;
      }
    }
  `;
  // 将样式添加到 iframe 内部的文档中的 <head> 元素中
  iframeDoc.head.appendChild(style);
  // 将内容插入到 iframe 中
  iframeDoc.body.innerHTML = printContainer.value;
  // generateCode()
  // nextTick(()=>{
  //   iframeRef.value.contentWindow.print()
  // })
  iframeRef.value.contentWindow.print()
  console.log(iframeRef.value, 'iframeRef.value');
  ;
}
defineExpose({
  print,
});
</script>
<style scoped lang="scss"></style>