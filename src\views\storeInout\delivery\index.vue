<template>
  <div>
    <!-- {{ userStore.getTenant }} -->
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #form-checkUser="{ model, field }">
        <ApiSelect :api="getSearchUserList" v-model:value="model[field]" allowClear showSearch placeholder="请输入验收人"
          optionFilterProp="label" labelField="realname" valueField="username" resultField="result" :params="searchParams"
          @search="onSearch"  @click="onClick" :filterOption="false">
        </ApiSelect>
      </template>
      <template #tableTitle>
        <a-button type="primary" v-if="hasPermission('delivery:checkOrderPrint')" @click="handleCheckOrderPrint">打印验收单</a-button>
      </template>
      <template #invoiceInfo>
        <span style="color:#1890ff;cursor: pointer;" @click="invoiceDeatil">查看</span>
      </template>
      <template #thirdcheckstatus="{ record }">
        <Tag :color="statusColor[record.thirdCheckStatus]">
          {{ record.thirdCheckStatus_dictText }}
        </Tag>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)">

        </TableAction>
      </template>
      <template #footer="currentPageData">
        <div class="foot pd6 dpflex jcsb">
          <div>合计</div>
          <div class="dpflex">
            <div class="ml100">{{ `${checkSumAmount}` }}</div>
            <div class="foot-total">{{ getFootTotal(currentPageData) }}</div>
        </div>
      </div>
    </template>
    </BasicTable>
    <div class="wrapper" ref="wrapper">
      <!-- 表单区域 -->
      <SpdDeliveryDrawer v-bind="$attrs" @register="registerDrawer" @success="handleSuccess"
        :getContainer="() => wrapper">
      </SpdDeliveryDrawer>
      <!-- 发票号modal -->
      <invoice-num-modal v-bind="$attrs" @register="InvoiceModal" :getContainer="() => wrapper"></invoice-num-modal>
    </div>
    <CheckOrderPrint :list="printList" :columns="printColumns" ref="checkOrderPrintRef"></CheckOrderPrint>
  </div>
</template>
<script lang="ts" setup name="storeInout-delivery">
import { ref, computed } from 'vue';
import { Tag } from 'ant-design-vue';
import { BasicTable, TableAction } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage';
import CheckOrderPrint from '/@/components/Print/SelfGoodsPrint/CheckOrderPrint.vue';
import SpdDeliveryDrawer from './components/SpdDeliveryDrawer.vue';
import { useDrawer } from '/@/components/Drawer';
import { columns, searchFormSchema, printColumns } from './SpdDelivery.data';
import { list, deliveryEnd, printDeliveryCheckOrder } from './SpdDelivery.api';
import InvoiceNumModal from '../../settlement/settleReview/components/InvoiceNumModal.vue';
import { useModal } from '/@/components/Modal';
import { ApiSelect } from '/@/components/Form/index';
import { useApiSelect } from '/@/spdHooks/useApiSelect'
import { getSearchUserList } from '/@/api/common/api';
import { usePermission } from '/@/hooks/web/usePermission';
import { useMessage } from '/@/hooks/web/useMessage';
const { createMessage } = useMessage();
const { hasPermission } = usePermission();
const { onSearch , onClick , searchParams } = useApiSelect()
import { handleSummaryNew, checkedSum } from '/@/utils/index'
const getFootTotal = (currentPageData)=> handleSummaryNew(currentPageData,'totalAmount','totalNum')
const checkSumAmount = computed(() => checkedSum(selectedRows.value,'totalAmount','totalNum'))
const statusColor = {
  0: '#d9d9d9', // 未验收
  1: '#87d068', // 已验收
  2: '#6cfd90', // 部分验收
  3: '#f50', // 已拒收
}
let wrapper = ref(null)
//注册drawer
const [registerDrawer, { openDrawer }] = useDrawer();
const [InvoiceModal, { openModal: InvoiceOpenModal }] = useModal(); // 发票号modal
//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    title: '配送单',
    api: list,
    columns,
    canResize: false,
    scroll:{y:400},
    showIndexColumn: true,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToNumber: [
      ],
      fieldMapToTime: [
        ['checkDateTime', ['checkDateTime_begin', 'checkDateTime_end'], 'YYYY-MM-DD'],
        ['deliveryDate', ['deliveryDate_begin', 'deliveryDate_end'], 'YYYY-MM-DD'],
      ],
    },
    actionColumn: {
      width: 240,
      fixed: 'right'
    },
    // 请求之前对参数做处理
    beforeFetch(params) {
      // params.checkStatus_MultiString = '0,1'
      params.column = 'checkStatus,' + params.column
      params.order = 'asc,' + params.order
      params.settlementType = '1'
    },
  },
})
const [registerTable, { reload ,clearSelectedRowKeys}, { rowSelection, selectedRows }] = tableContext

/**
 * 成功回调
 */
function handleSuccess() {
  reload();
  clearSelectedRowKeys()
}


/**
 * 检查基础条件（未结束且非配送中）
 */
function checkBaseConditions(record: Recordable) {
  return record.endFlag !== 1 && record.deliveryStatus !== 2;
}

/**
   * 操作栏
   */
function getTableAction(record) {
  return [
    {
      label: '继续验收',
      onClick: contAccept.bind(null, record, 'accept'),
      ifShow: (_action) => {
        return record.checkStatus === 1 && checkBaseConditions(record);
      },
    },
    {
      label: '验收',
      onClick: goAccept.bind(null, record, 'accept'),
      ifShow: (_action) => {
        if(record.checkStatus == 0 && record.endFlag != 1 && record.deliveryStatus != 2) { 
          if(record.thirdPartyFlag === 0 ) return true
          if(record.thirdPartyFlag === 1 && record.thirdCheckStatus!=0 && record.thirdParty === 0 ){
            return true
          }
          return false
          }
      },
    },
    {
      label: '结束',
      popConfirm: {
        title: '是否确认结束',
        confirm: over.bind(null, record, 'end'),
      },
      ifShow: (_action) => {
        if( record.thirdPartyFlag === 0 && record.endFlag !== 1 && record.deliveryStatus != 2)  return true
        if( record.thirdPartyFlag === 1 && record.thirdCheckStatus!=0 && record.thirdParty === 0 && record.endFlag !== 1 && record.deliveryStatus != 2)  return true
        return false
      },
    },
    {
      label: '查看',
      onClick: lookDetail.bind(null, record, 'check'),
    },
  ]
}

const invoiceDeatil = (record: Recordable) => {
  InvoiceOpenModal(true, {
    record,
    isUpdate: false,
    showFooter: false,
  });
}
/**
  * 配送单号查看详情
 */
const lookDetail = (record: Recordable, type) => {
  record.type = type
  openDrawer(true, {
    record,
    isUpdate: false,
    showFooter: false,
  });
}
// 继续验收
const contAccept = (record: Recordable, type) => {
  record.type = type
  openDrawer(true, {
    record,
    isUpdate: false,
    showFooter: false,
  });
}
// 结束
const over = async (record: Recordable, type) => {
  // console.log('结束', type);
  await deliveryEnd({ id: record.id, endFlag: 1 },);
  handleSuccess()
}
// 验收
const goAccept = (record: Recordable, type) => {
  record.type = type
  openDrawer(true, {
    record,
    isUpdate: false,
    showFooter: false,
  });
}

const printList = ref<any>([])
const checkOrderPrintRef = ref<any>(null)
// 打印验收单
const handleCheckOrderPrint = async () =>{
  try{
    if(!selectedRows.value.length) return createMessage.error('请选择需要打印的单据')
    const res = await printDeliveryCheckOrder({
      deliveryIds: selectedRows.value.map(item => item.id),
      size: 12
    })
    printList.value = res
    setTimeout(()=>{
      checkOrderPrintRef.value.print()
    },200)
  }catch(err){
    console.log(err,'err');
  }finally{
  }
}
</script>
<style scoped lang="scss">
.foot{
  .foot-total{
    text-align: right;
  }
}
:v-deep(.ant-table-body){
  overflow: hidden;
}
</style>
