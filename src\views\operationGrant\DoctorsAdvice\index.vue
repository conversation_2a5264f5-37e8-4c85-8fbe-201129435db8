<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <!--<a-button type="primary" @click="getExcel">导出</a-button>-->
      </template>

    </BasicTable>
  </div>
</template>

<script lang="ts" name="operationGrant-DoctorsAdvice" setup>

import { BasicTable } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage'
import { columns, searchFormSchema } from './index.data';
import { list } from './index.api';


//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    api: list,
    columns,
    canResize: false,
    ellipsis: true,
    scroll:{y:500},
    showIndexColumn: true,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToNumber: [
      ],
      fieldMapToTime: [
        ['billDateTime', ['billDateTime_begin', 'billDateTime_end'], 'YYYY-MM-DD']
      ],
    },
    showActionColumn: false,
    beforeFetch(params) {
      //if (params.departName){params.departCode=params.departName}else{params.departCode=null}
      //if (params.wardDepartName){params.wardDepartCode=params.wardDepartName}else{params.wardDepartCode=null}
      //if (params.executiveDepartName){params.executiveDepartCode=params.executiveDepartName}else{params.executiveDepartCode=null}
      //if (params.billDepartName){params.billDepartCode=params.billDepartName}else{params.billDepartCode=null}
      //if (params.sendDepartName){params.sendDepartCode=params.sendDepartName}else{params.sendDepartCode=null}
    }
  },
})

const [registerTable, { reload }, { selectedRowKeys }] = tableContext


/**
 * 成功回调
 */
//function handleSuccess() {
//  (selectedRowKeys.value = []) && reload();
//}
</script>
<style scoped>
</style>
