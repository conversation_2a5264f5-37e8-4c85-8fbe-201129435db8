import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
// import { queryStoreList } from '/@/views/settlement/settleReview/settleManage.api'
import { getDepartListLeaf, suppliernoPagelist, getspdStoragelist } from '/@/api/common/api';
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore(); 
import { ranges } from "/@/api/common/data";
import { FormatNumToThousands } from '/@/utils/index'
import { usePermission } from '/@/hooks/web/usePermission';
const { hasPermission } = usePermission();
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '配送单号',
    align: 'center',
    dataIndex: 'deliveryOrderNo',
    sorter: true,
    width:180,
    resizable:true,
  },
  {
    title: '订单号',
    align: 'center',
    dataIndex: 'orderNo',
    width: 200,
    resizable:true,
    sorter: true,
  },
  {
    title: '申领科室',
    align: 'center',
    dataIndex: 'applyDepartName',
    width:200,
    resizable:true,
  },

  {
    title: '采购库房',
    align: 'center',
    dataIndex: 'purchaseStorageName',
    width:180,
    resizable:true,
  },
  {
    title: '总数量',
    align: 'center',
    dataIndex: 'totalNum',
    width: 80,
    resizable:true,
    sorter: true,
  },
  {
    title: '总金额',
    align: 'center',
    dataIndex: 'totalAmount',
    width: 150,
    resizable:true,
    sorter: true,
    customRender: ({ text }) => {
      return FormatNumToThousands(text);
    },
  },
  {
    title: '状态',
    align: 'center',
    dataIndex: 'checkStatus_dictText',
    sorter: true,
    width:100,
    resizable:true,
  },
  {
    title: '第三方验收状态',
    align: 'center',
    dataIndex: 'thirdcheckstatus',
    width:120,
    auth: 'delivery:thirdCheckStatusClomn',
    slots: { customRender: 'thirdcheckstatus' },
  },
  {
    title: '配送单状态',
    align: 'center',
    dataIndex: 'deliveryStatus_dictText',
    sorter: true,
    width:120,
    resizable:true,
  },
  {
    title: '打印次数',
    align: 'center',
    dataIndex: 'transferPrintTimes',
    width:120,
    resizable:true,
  },
  {
    title: '供应商',
    align: 'center',
    dataIndex: 'supplierName',
    width:200,
    resizable:true,
  },
  {
    title: '验收人',
    align: 'center',
    dataIndex: 'checkUser',
    width:100,
    resizable:true,
  },
  {
    title: '验收时间',
    align: 'center',
    dataIndex: 'checkDateTime',
    sorter: true,
    width:100,
    resizable:true,
  },
  {
    title: '结算类型',
    align: 'center',
    dataIndex: 'settlementType_dictText',
    width:100,
    resizable:true,
  },
  {
    title: '配送员',
    align: 'center',
    dataIndex: 'dispatcher',
    width:100,
    resizable:true,
  },

  {
    title: '配送日期',
    align: 'center',
    dataIndex: 'deliveryDate',
    sorter: true,
    width:100,
    resizable:true,
  },
  {
    title: '备注',
    align: 'center',
    dataIndex: 'remark',
    width:150,
    resizable:true,
  },
];
export const printColumns = [
  {
    title: "序号",
    dataIndex:'sortNum',
    width: 50
  },
  {
    title: '物资编码',
    dataIndex: 'goodsCode',
    width: 80
  },
  {
    title: '物资名称',
    dataIndex: 'goodsName',
    width: 150,
    slots: { customRender: 'goodsName' },
  },
  {
    title: '规格型号',
    dataIndex: 'goodsSpecsDetail',
    width: 100,
    slots: { customRender: 'goodsSpecsDetail' },
  },
  {
    title: '单位',
    dataIndex: 'unitName',
    width: 50
  },
  {
    title: '单价',
    dataIndex: 'goodsPrice',
    width: 50
  },
  {
    title: '金额',
    dataIndex: 'totalAmount',
    width: 80
  },
  {
    title: '批号',
    dataIndex: 'batchNo',
    width: 80
  },
  {
    title: '效期',
    dataIndex: 'term',
    width: 80
  },
  {
    title: '注册证号',
    dataIndex: 'registerNo',
    width: 100
  },
  {
    title: '是否符合验收标准',
    dataIndex: 'checkResult',
    width: 120
  },
  {
    title: '送货数量',
    dataIndex: 'deliveryNum',
    width: 60
  },
  {
    title: '验收数量',
    dataIndex: 'checkNum',
    width: 60
  },
]
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '配送单号',
    field: 'deliveryOrderNo',
    component: 'JInput',
  },
  {
    field: 'checkDateTime',
    component: 'RangePicker',
    label: '验收时间',
    colProps: {
      span: 8,
    },
    componentProps: { valueType: 'Date', ranges: ranges}
  },
  {
    field: 'deliveryDate',
    component: 'RangePicker',
    label: '配送时间',
    colProps: {
      span: 8,
    },
    componentProps: { valueType: 'Date', ranges: ranges}
  },
  {
    label: '库房',
    field: 'purchaseStorageId',
    component: 'ApiSelect',
    componentProps: {
      type: 'like',
      value: [],
      api: () => getspdStoragelist({}),
      showSearch: true,
      optionFilterProp: 'storageNameAbbr',
      labelField: 'storageName', // label值
      // resultField:'records', //  接口返回的字段，如果接口返回数组，可以不填。支持x.x.x格式
      valueField: 'id', // value值
      // mode: "multiple", // 支持多选
      allowClear: true
    },
  },
  {
    label: '状态',
    field: 'checkStatus',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'delivery_check_status',
      };
    },
    colProps: {
      span: 4,
    },
  },
  {
    label: '订单号',
    field: 'orderNo',
    component: 'JInput',
  },
  {
    label: '验收人',
    field: 'checkUserName',
    component: 'JInput',
    slot: 'checkUser'
  },
  // {
  //   label: '结算类型',
  //   field: 'settlementType',
  //   component: 'JDictSelectTag',
  //   componentProps: () => {
  //     return {
  //       dictCode: 'settlementbills_type',
  //     };
  //   },
  //   colProps: {
  //     span: 4,
  //   },
  // },
  {
    label: '申领科室',
    field: 'applyDepartId',
    component: 'ApiSelect',
    componentProps: {
      api: getDepartListLeaf,
      params: {},
      showSearch: true,
      optionFilterProp: 'departNameDepartNameAbbr',
      labelField: 'departName',
      valueField: 'id',
      allowClear: true,
    },
  },
  {
    label: '供应商',
    field: 'supplierId',
    component: 'ApiSelect',
    componentProps: {
      api: suppliernoPagelist,
      labelField: 'supplierName',
      valueField: 'id',
      showSearch: true,
      optionFilterProp: "supplierNameSupplyPycode",
    },
    colProps: { span: 6 },
  },
  {
    label: '物资',
    field: 'goodsKeyWord',
    component: 'Input',
    componentProps: {
      placeholder: '请输入物资名称或规格',
    }
  },
  {
    label: '第三方验收状态',
    field: 'thirdCheckStatus',
    component: 'JDictSelectTag',
    ifShow: ({ values }) =>  hasPermission('delivery:thirdCheckStatusForm'), 
    componentProps: () => {
      return {
        dictCode: 'delivery_check_status',
      };
    },
  },
];

//SpdDeliveryDrawer.vue
export const columnsD = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: 'center',
    width: 80,
    slots: { customRender: 'serialNum' },
  },
  { title: '选择', width: 80, slots: { customRender: 'check' }, align: 'center' },
  {
    title: "物资编码",
    dataIndex: "goodsCode",
    key: "goodsCode",
    width: 160,
    align: 'center'
  },
  {
    title: "批次码/定数包码",
    dataIndex: "batchCode",
    key: "batchCode",
    width: 160,
    align: 'center'
  },
  {
    title: "是否定数包",
    dataIndex: "quantitativePackageFlag_dictText",
    width: 160,
    align: 'center'
  },
  {
    title: "定数包规格",
    dataIndex: "quantitativePackageSpecs",
    key: "quantitativePackageSpecs",
    width: 160,
    // ellipsis: true, // 设置ellipsis属性为true
    align: 'center'
  },
  {
    title: "物资名称",
    dataIndex: "goodsName",
    key: "goodsName",
    width: 160,
    // ellipsis: true, // 设置ellipsis属性为true
    align: 'center'
  },
  {
    title: "送货数量",
    dataIndex: "deliveryNum",
    key: "deliveryNum",
    width: 100,
    align: 'center'
  },
  {
    title: "已验收数量",
    dataIndex: "checkNum",
    key: "checkNum",
    width: 120,
    align: 'center'
  },
  {
    title: "本次验收数量",
    dataIndex: "currentCheckNum",
    key: "currentCheckNum",
    slots: { customRender: 'currentNum' },
    width: 120,
    align: 'center'
  },
  {
    title: "第三方验收状态",
    dataIndex: "thirdCheckStatus_dictText",
    key: "thirdCheckStatus_dictText",
    width: 130,
    align: 'center'
  },
  {
    title: "批号",
    dataIndex: "batchNo",
    key: "batchNo",
    width: 120,
    align: 'center'
  },
  {
    title: "规格",
    dataIndex: "goodsSpecs",
    key: "goodsSpecs",
    width: 120,
    align: 'center'
  },
  {
    title: "型号",
    dataIndex: "goodsSpecsDetail",
    key: "goodsSpecsDetail",
    width: 120,
    align: 'center'
  },
  {
    title: "单位",
    dataIndex: "unitName",
    key: "unitName",
    align: 'center',
    width: 120
  },
  {
    title: "单价",
    dataIndex: "goodsPrice",
    key: "goodsPrice",
    align: 'center',
    width: 100
  },
  {
    title: "金额",
    dataIndex: "totalAmount",
    key: "totalAmount",
    align: 'center',
    width: 120
  },
  {
    title: "生产日期",
    dataIndex: "productDate",
    key: "productDate",
    align: 'center',
    width: 120
  },
  {
    title: "有效期",
    dataIndex: "term",
    key: "term",
    align: 'center',
    width: 120
  },
  {
    title: "注册证号",
    dataIndex: "registerNo",
    key: "registerNo",
    align: 'center',
    width: 120
  },
  {
    title: "采购库房",
    dataIndex: "createdAt",
    key: "createdAt",
    align: 'center',
    width: 120
  },
  {
    title: "生产厂家",
    dataIndex: "manufacturerName",
    key: "manufacturerName",
    align: 'center',
    ellipsis: true,
    width: 160
  },
  {
    title: "内外包装是否完整",
    dataIndex: "packagingIntegrityDefectiveNum",
    key: "packagingIntegrityDefectiveNum",
    slots: { customRender: 'packagingIntegrityDefectiveNum' },
    width: 120,
    align: 'center'
  },
  {
    title: "是否标签清晰",
    dataIndex: "labelClarityDefectiveNum",
    key: "labelClarityDefectiveNum",
    slots: { customRender: 'labelClarityDefectiveNum' },
    width: 120,
    align: 'center'
  },
  {
    title: "是否有异物",
    dataIndex: "foreignMatterDefectiveNum",
    key: "foreignMatterDefectiveNum",
    slots: { customRender: 'foreignMatterDefectiveNum' },
    width: 120,
    align: 'center'
  },
  {
    title: "灭菌日期",
    dataIndex: "sterilizationDate",
    key: "sterilizationDate",
    align: 'center',
    width: 120
  },
  {
    title: "灭菌批号",
    dataIndex: "sterilizationBatch",
    key: "sterilizationBatch",
    align: 'center',
    width: 120
  },
  {
    title: "储运条件",
    dataIndex: "storageTypeName",
    key: "storageTypeName",
    align: 'center',
    width: 120
  },
  {
    title: "说明备注",
    dataIndex: "remark",
    key: "remark",
    align: 'center',
    width: 120
  },
  { title: '操作', key: 'customAction', fixed: 'right', width: 150, slots: { customRender: 'customAction' }, align: 'center' },
];
export const columnsDS = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: 'center',
    width: 80,
    slots: { customRender: 'serialNum' },
  },
  { title: '选择', width: 80, slots: { customRender: 'check' }, align: 'center' },
  {
    title: "物资编码",
    dataIndex: "goodsCode",
    key: "goodsCode",
    width: 160,
    align: 'center'
  },
  {
    title: "批次码/定数包码",
    dataIndex: "batchCode",
    key: "batchCode",
    width: 160,
    align: 'center'
  },
  {
    title: "是否定数包",
    dataIndex: "quantitativePackageFlag_dictText",
    width: 160,
    align: 'center'
  },
  {
    title: "定数包规格",
    dataIndex: "quantitativePackageSpecs",
    key: "quantitativePackageSpecs",
    width: 160,
    // ellipsis: true, // 设置ellipsis属性为true
    align: 'center'
  },
  {
    title: "物资名称",
    dataIndex: "goodsName",
    key: "goodsName",
    width: 160,
    // ellipsis: true, // 设置ellipsis属性为true
    align: 'center'
  },
  {
    title: "送货数量",
    dataIndex: "deliveryNum",
    key: "deliveryNum",
    width: 100,
    align: 'center'
  },
  {
    title: "已验收数量",
    dataIndex: "checkNum",
    key: "checkNum",
    width: 120,
    align: 'center'
  },
  {
    title: "本次验收数量",
    dataIndex: "currentCheckNum",
    key: "currentCheckNum",
    slots: { customRender: 'currentNum' },
    width: 120,
    align: 'center'
  },
  {
    title: "第三方验收状态",
    dataIndex: "thirdCheckStatus_dictText",
    key: "thirdCheckStatus_dictText",
    width: 130,
    align: 'center'
  },
  {
    title: "批号",
    dataIndex: "batchNo",
    key: "batchNo",
    width: 120,
    align: 'center'
  },
  {
    title: "规格",
    dataIndex: "goodsSpecs",
    key: "goodsSpecs",
    width: 120,
    align: 'center'
  },
  {
    title: "型号",
    dataIndex: "goodsSpecsDetail",
    key: "goodsSpecsDetail",
    width: 120,
    align: 'center'
  },
  {
    title: "单位",
    dataIndex: "unitName",
    key: "unitName",
    align: 'center',
    width: 120
  },
  {
    title: "金额",
    dataIndex: "totalAmount",
    key: "totalAmount",
    align: 'center',
    width: 120
  },
  {
    title: "单价",
    dataIndex: "goodsPrice",
    key: "goodsPrice",
    align: 'center',
    width: 100
  },
  {
    title: "生产日期",
    dataIndex: "productDate",
    key: "productDate",
    align: 'center',
    width: 120
  },
  {
    title: "有效期",
    dataIndex: "term",
    key: "term",
    align: 'center',
    width: 120
  },
  {
    title: "注册证号",
    dataIndex: "registerNo",
    key: "registerNo",
    align: 'center',
    width: 120
  },
  {
    title: "采购库房",
    dataIndex: "createdAt",
    key: "createdAt",
    align: 'center',
    width: 120
  },
  {
    title: "生产厂家",
    dataIndex: "manufacturerName",
    key: "manufacturerName",
    align: 'center',
    ellipsis: true,
    width: 160
  },
  {
    title: "内外包装是否完整",
    dataIndex: "packagingIntegrityDefectiveNum",
    key: "packagingIntegrityDefectiveNum",
    slots: { customRender: 'packagingIntegrityDefectiveNum' },
    width: 120,
    align: 'center'
  },
  {
    title: "是否标签清晰",
    dataIndex: "labelClarityDefectiveNum",
    key: "labelClarityDefectiveNum",
    slots: { customRender: 'labelClarityDefectiveNum' },
    width: 120,
    align: 'center'
  },
  {
    title: "是否有异物",
    dataIndex: "foreignMatterDefectiveNum",
    key: "foreignMatterDefectiveNum",
    slots: { customRender: 'foreignMatterDefectiveNum' },
    width: 120,
    align: 'center'
  },
  {
    title: "灭菌日期",
    dataIndex: "sterilizationDate",
    key: "sterilizationDate",
    align: 'center',
    width: 120
  },
  {
    title: "灭菌批号",
    dataIndex: "sterilizationBatch",
    key: "sterilizationBatch",
    align: 'center',
    width: 120
  },
  {
    title: "储运条件",
    dataIndex: "storageTypeName",
    key: "storageTypeName",
    align: 'center',
    width: 120
  },
  {
    title: "说明备注",
    dataIndex: "remark",
    key: "remark",
    align: 'center',
    width: 120
  },
  { title: '操作', key: 'customAction', fixed: 'right', width: 150, slots: { customRender: 'customAction' }, align: 'center' },
];
export const innerColumnsD = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: 'center',
    width: 30,
    slots: { customRender: 'serialNum' },
    // resizable: true,
  },
  { title: '选择', slots: { customRender: 'check' }, align: 'center', width: 30, },
  {
    title: "物资编码",
    dataIndex: "goodsCode",
    align: 'center',
    width: 60,
    // resizable: true,
  },

  {
    title: "物资名称",
    dataIndex: "goodsName",
    align: 'center',
    width: 80,
    // ellipsis: true, // 设置ellipsis属性为true
    // resizable: true,
  },
  {
    title: "物资条码",
    dataIndex: "uniqueCode",
    align: 'center',
    width: 80,
    slots: { customRender: 'uniqueCode' },
    // resizable: true,

  },
  {
    title: "UDI",
    dataIndex: "udiCode",
    key: "udiCode",
    align: 'center',
    slots: { customRender: 'udiCode' },
    width: 100
  },
  {
    title: "RFID",
    dataIndex: "rfid",
    key: "rfid",
    align: 'center',
    width: 100
  },
  {
    title: "验收状态",
    dataIndex: "checkStatus_dictText",
    align: 'center',
    width: 50,
    // resizable: true,
  },
  {
    title: "普通条码打印次数",
    dataIndex: "commonTagPrintTimes",
    align: 'center',
    width: 100,
    // resizable: true,
  },
  {
    title: "RFID打印次数",
    dataIndex: "rfidTagPrintTimes",
    align: 'center',
    width: 100,
    // resizable: true,
  },
  {
    title: "第三方验收状态",
    dataIndex: "thirdCheckStatus_dictText",
    key: "thirdCheckStatus_dictText",
    width: 80,
    align: 'center'
  },
  {
    title: "批号",
    dataIndex: "batchNo",
    key: "batchNo",
    width: 60,
    slots: { customRender: 'batchNo' },
    align: 'center'
  },
  {
    title: "规格",
    dataIndex: "goodsSpecs",
    align: 'center',
    width: 60,
    // resizable: true,

  },
  {
    title: "型号",
    dataIndex: "goodsSpecsDetail",
    key: "goodsSpecsDetail",
    width: 120,
    align: 'center'
  },
  {
    title: "生产日期",
    dataIndex: "productDate",
    align: 'center',
    width: 60,
    slots: { customRender: 'productDate' },
    // resizable: true,

  },
  {
    title: "有效期",
    dataIndex: "term",
    align: 'center',
    width: 50,
    slots: { customRender: 'term' },
    // resizable: true,

  },
  {
    title: "单位",
    dataIndex: "unitName",
    align: 'center',
    width: 30,
    // resizable: true,

  },
  {
    title: "单价",
    dataIndex: "goodsPrice",
    align: 'center',
    width: 40,
    // resizable: true,

  },

  {
    title: "生产厂家",
    dataIndex: "manufacturerName",
    align: 'center',
    width: 80,
    ellipsis: true,
    // resizable: true,

  },
  {
    title: "说明备注",
    dataIndex: "remark",
    align: 'center',
    width: 60,
    // resizable: true,

  },
  {
    title: "内外包装是否完整",
    dataIndex: "packagingIntegrityFlag",
    key: "packagingIntegrityFlag",
    slots: { customRender: 'packagingIntegrityFlag' },
    align: 'center',
    width: 50,
    resizable: true,
  },
  {
    title: "是否标签清晰",
    dataIndex: "labelClarityFlag",
    key: "labelClarityFlag",
    slots: { customRender: 'labelClarityFlag' },
    align: 'center',
    width: 50,
    resizable: true,
  },
  {
    title: "是否有异物",
    dataIndex: "foreignMatterFlag",
    key: "foreignMatterFlag",
    slots: { customRender: 'foreignMatterFlag' },
    align: 'center',
    width: 50,
    resizable: true,
  },
  {
    title: "验收是否合格",
    dataIndex: "checkFlag",
    key: "checkFlag",
    slots: { customRender: 'checkFlag' },
    align: 'center',
    width: 50,
    resizable: true,
  },
  { title: '操作', key: 'customAction', fixed: 'right', slots: { customRender: 'customAction' }, align: 'center', width: 60 },
  // { title: '操作', key: 'customAction', fixed: 'right', width: 160, slots: { customRender: 'customAction' }, align: 'center',   resizable: true,},
];
export const innerColumnsD2S = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    align: 'center',
    width: 30,
    slots: { customRender: 'serialNum' },
    // resizable: true,
  },
  { title: '选择', slots: { customRender: 'check' }, align: 'center', width: 30, },
  {
    title: "物资编码",
    dataIndex: "goodsCode",
    align: 'center',
    width: 60,
    // resizable: true,
  },

  {
    title: "物资名称",
    dataIndex: "goodsName",
    align: 'center',
    width: 80,
    // ellipsis: true, // 设置ellipsis属性为true
    // resizable: true,
  },
  {
    title: "物资条码",
    dataIndex: "uniqueCode",
    align: 'center',
    width: 80,
    // resizable: true,

  },
  {
    title: "UDI",
    dataIndex: "udiCode",
    key: "udiCode",
    align: 'center',
    width: 100
  },
  {
    title: "RFID",
    dataIndex: "rfid",
    key: "rfid",
    align: 'center',
    width: 100
  },
  {
    title: "验收状态",
    dataIndex: "checkStatus_dictText",
    align: 'center',
    width: 50,
    // resizable: true,

  },
  {
    title: "普通条码打印次数",
    dataIndex: "commonTagPrintTimes",
    align: 'center',
    width: 100,
    // resizable: true,
  },
  {
    title: "RFID打印次数",
    dataIndex: "rfidTagPrintTimes",
    align: 'center',
    width: 100,
    // resizable: true,
  },
  {
    title: "第三方验收状态",
    dataIndex: "thirdCheckStatus_dictText",
    key: "thirdCheckStatus_dictText",
    width: 80,
    align: 'center'
  },
  {
    title: "批号",
    dataIndex: "batchNo",
    key: "batchNo",
    width: 60,
    slots: { customRender: 'batchNo' },
    align: 'center'
  },
  {
    title: "规格",
    dataIndex: "goodsSpecs",
    align: 'center',
    width: 60,
    // resizable: true,

  },
  {
    title: "型号",
    dataIndex: "goodsSpecsDetail",
    key: "goodsSpecsDetail",
    width: 120,
    align: 'center'
  },
  {
    title: "生产日期",
    dataIndex: "productDate",
    align: 'center',
    width: 60,
    // resizable: true,

  },
  {
    title: "有效期",
    dataIndex: "term",
    align: 'center',
    width: 50,
    // resizable: true,

  },
  {
    title: "单位",
    dataIndex: "unitName",
    align: 'center',
    width: 30,
    // resizable: true,

  },
  {
    title: "单价",
    dataIndex: "goodsPrice",
    align: 'center',
    width: 40,
    // resizable: true,

  },

  {
    title: "生产厂家",
    dataIndex: "manufacturerName",
    align: 'center',
    width: 80,
    ellipsis: true,
    // resizable: true,

  },
  {
    title: "说明备注",
    dataIndex: "remark",
    align: 'center',
    width: 60,
    // resizable: true,

  },
  {
    title: "内外包装是否完整",
    dataIndex: "packagingIntegrityFlag",
    key: "packagingIntegrityFlag",
    slots: { customRender: 'packagingIntegrityFlag' },
    align: 'center',
    width: 50,
    resizable: true,
  },
  {
    title: "是否标签清晰",
    dataIndex: "labelClarityFlag",
    key: "labelClarityFlag",
    slots: { customRender: 'labelClarityFlag' },
    align: 'center',
    width: 50,
    resizable: true,
  },
  {
    title: "是否有异物",
    dataIndex: "foreignMatterFlag",
    key: "foreignMatterFlag",
    slots: { customRender: 'foreignMatterFlag' },
    align: 'center',
    width: 50,
    resizable: true,
  },
  {
    title: "验收是否合格",
    dataIndex: "checkFlag",
    key: "checkFlag",
    slots: { customRender: 'checkFlag' },
    align: 'center',
    width: 50,
    resizable: true,
  },
  { title: '操作', key: 'customAction', fixed: 'right', slots: { customRender: 'customAction' }, align: 'center', width: 60 },
];
