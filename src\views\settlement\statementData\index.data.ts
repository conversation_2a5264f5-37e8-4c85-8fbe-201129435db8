import { BasicColumn, FormSchema } from '/@/components/Table';
export const columns: BasicColumn[] = [
  {
    title: '时间',
    align: 'center',
    dataIndex: 'month',
    width: 100,
  },
  {
    title: '供应商',
    align: 'center',
    dataIndex: 'supplierName',
    width: 150,
  },
  {
    title: '物资编码',
    align: 'center',
    dataIndex: 'goodsCode',
    width: 100,
  },
  {
    title: 'CL码',
    align: 'center',
    dataIndex: 'matClcode',
    width: 100,
  },
  {
    title: '物资名称',
    align: 'center',
    dataIndex: 'goodsName',
    width: 100,
  },
  {
    title: '规格',
    align: 'center',
    dataIndex: 'goodsSpecs',
    width: 100,
  },
  {
    title: '型号',
    align: 'center',
    dataIndex: 'goodsSpecsDetail',
    width: 100,
  },
  {
    title: '上月结存',
    align: 'center',
    dataIndex: 'lastMonthNum',
    width: 100,
  },
  {
    title: '配送数量',
    align: 'center',
    dataIndex: 'deliveryNum',
    width: 100,
  },
  {
    title: '计费数量',
    align: 'center',
    dataIndex: 'billNum',
    width: 100,
  },
  {
    title: '退货数量',
    align: 'center',
    dataIndex: 'returnNum',
    width: 100,
  },
  {
    title: '当月结存',
    align: 'center',
    dataIndex: 'lastNum',
    width: 100,
  },
  {
    title: '异常数量',
    align: 'center',
    dataIndex: 'errorNum',
    width: 100,
  },
];
export const formSchema: FormSchema[] = [
  { field: 'month', component: 'MonthPicker', label: '日期' },
  { field: 'goodsCode', component: 'JInput', label: '物资编码/CL码' },
  { field: 'goodsName', component: 'JInput', label: '物资名称' },
  { field: 'supplierName', component: 'JInput', label: '供应商' },
]