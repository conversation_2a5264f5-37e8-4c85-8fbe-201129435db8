<template>
  <div>
    <BasicModal v-bind="$attrs" @register="registerModal" :showCancelBtn="false" title="材料出库汇总" destroyOnClose
      @ok="handleOk" :width="1500" :maskClosable="false" :default-fullscreen="true">
      <div class="title" style="margin-bottom: 20px;">
        <span style="margin-right:40px;">出库仓库: &nbsp;&nbsp;{{ form.store }}</span>
        <span>申领仓库：&nbsp;&nbsp;{{ form.outStore }}</span>
      </div>
      <div>扫码出库：<a-input ref="iptRef" v-model:value="form.code" @keyup.enter="addRow" style="width:300px;" /></div>
      <div style="padding:10px 0;">
        <span style=" padding-right: 20px;">是否推送</span>
        <a-switch checked-children="是" un-checked-children="否" v-model:checked="form.pushFlag" />
      </div>
      <BasicTable class="ant-table-striped" :clickToRowSelect="false" :rowSelection="rowSelection" bordered rowKey="id" :columns="columns"
        :dataSource="form.tableData" :scroll="{ y: 500 }" :pagination="false" :rowClassName="rowClassName">
        <template #currentNum="{ record, index }">
          <span v-if="record.individualFlag === 1 && record.selected" style="color:#188ffe;cursor: pointer;"
            @click="openDetail(record, index)">{{ record.currentNum }}
          </span>
          <span v-if="record.individualFlag === 1 && !record.selected">{{ record.currentNum }}</span>
          <span v-if="record.individualFlag == 0">{{ record.currentNum }}</span>
        </template>
        <template #currentOutStoreNum="{ record }">
          <div v-if="record.individualFlag === 1"
            style="background-color:#188ffe;color: #fff; width: 80px;height: 30px;line-height: 30px;">
            {{ record.currentOutStoreNum ? record.currentOutStoreNum : 0 }}
          </div>
          <a-input v-if="record.individualFlag != 1 && record.selected" v-model:value="record.currentOutStoreNum"
            @input="input(record)" />
        </template>
        <template #footer="currentPageData">
          <div class="foot pd6 dpflex jcsb">
            <div>合计</div>
            <div class="foot-total">{{ getFootTotal(currentPageData) }}</div>
          </div>
        </template>
      </BasicTable>
      <!-- 耗材Modal -->
      <GoodsModal @getCheckData="getCheckData" @register="CheckGoods"></GoodsModal>
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, defineEmits, computed } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicColumn } from '/@/components/Table';
import { queryPageTotalList, addByApplyOrder } from '../SpdDeliveryOut.api'
import { inventoryDetailPageList } from '/@/api/common/api';
import { BasicTable } from '/@/components/Table';
import { message } from 'ant-design-vue';
import { useModal } from '/@/components/Modal';
import GoodsModal from './GoodsModal.vue'
import { getNumToThousands } from "/@/utils/index";
const emit = defineEmits(['success', 'resetRows']);
const getFootTotal = (currentPageData) =>
  `${handleSummary(currentPageData).totalNum} ,\xa0` +
  `${handleSummary(currentPageData).totalAmount}`;
const iptRef = ref()
const [CheckGoods, { openModal: GoodsOpenModal }] = useModal(); // 耗材条码明细
const searchInfo = ref(
  { column: 'goodsCode', order: 'asc' }
)
const form = reactive<any>({
  outStore: '',
  sourceStorageId: '',
  targetStorageId: '',
  pushFlag: true,
  store: '',
  code: '',
  tableData: [],
  applyOrderIds: []
})
const columns: BasicColumn[] = [
  {
    title: '物资编码',
    align: 'center',
    dataIndex: 'goodsCode',
    key: 'goodsCode',
    width: 160,
    ellipsis: true, // 设置ellipsis属性为true
  },
  {
    title: '物资名称',
    align: 'center',
    dataIndex: 'goodsName',
    key: 'goodsName',
    width: 160,
    ellipsis: true, // 设置ellipsis属性为true
  },
  {
    title: '是否个体码',
    align: 'center',
    dataIndex: 'individualFlag_dictText',
    key: 'individualFlag_dictText',
    width: 120,
    ellipsis: true, // 设置ellipsis属性为true
  },
  {
    title: '规格',
    align: 'center',
    dataIndex: 'goodsSpecs',
    key: 'goodsSpecs',
    width: 120,
    ellipsis: true, // 设置ellipsis属性为true
  },
  {
    title: '型号',
    align: 'center',
    dataIndex: 'goodsSpecsDetail',
    key: 'goodsSpecsDetail',
    width: 120,
    ellipsis: true, // 设置ellipsis属性为true
  },
  {
    title: '单位',
    width: 100,
    align: 'center',
    dataIndex: 'unitName',
    key: 'unitName',
  },
  {
    title: '申领数量',
    width: 100,
    align: 'center',
    dataIndex: 'applyNum',
    key: 'applyNum',
  },
  {
    title: '已发放数量',
    width: 120,
    align: 'center',
    dataIndex: 'distributionNum',
    key: 'distributionNum',
  },
  {
    title: '待出库数量',
    width: 120,
    align: 'center',
    dataIndex: 'waitDistributionNum',
    key: 'waitDistributionNum',
  },
  {
    title: '即时库存',
    width: 100,
    align: 'center',
    dataIndex: 'currentNum',
    key: 'currentNum',
    slots: { customRender: 'currentNum' },
  },
  {
    title: '出库数量',
    width: 100,
    align: 'center',
    dataIndex: 'currentOutStoreNum',
    key: 'currentOutStoreNum',
    slots: { customRender: 'currentOutStoreNum' },
  },
];
const [registerModal, { setModalProps, closeModal, changeOkLoading },] = useModalInner(async (data) => {
  setModalProps({ confirmLoading: false, showCancelBtn: !!data?.showFooter, showOkBtn: !!data?.showFooter, okText: '确认出库' });
  changeOkLoading(false)
  form.tableData = []
  checkedKeys.value = []
  checkedRows.value = []
  const { requestStorageName, parentStorageName, requestStorageId, parentStorageId } = data.record[0]
  iptRef.value.focus();
  form.outStore = requestStorageName
  form.store = parentStorageName
  form.sourceStorageId = parentStorageId
  form.targetStorageId = requestStorageId
  form.applyOrderIds = data.applyOrderIds
  let str = data.applyOrderIds.join(",")
  const res = await queryPageTotalList({ applyOrderIds: str, ...searchInfo.value })
  form.tableData = res.records
  form.tableData.forEach(item => {
    item.additionalData = []
    item.checkedKeys = []
    item.selected = false

  })
})
const openDetail = (record, index) => {
  GoodsOpenModal(true, {
    record,
    index,
    isUpdate: false,
    showFooter: true,
  });
}
const input = (record) => {
  if (+record.currentOutStoreNum > +record.currentNum || +record.currentOutStoreNum > +record.waitDistributionNum) {
    record.currentOutStoreNum = 0
    message.warning('出库数量不能大于即时库存或待出库数量')
  }
}
// 扫码匹配
const addRow = async () => {
  if (form.code === "") {
    return;
  }
  try {
    const res = await inventoryDetailPageList({ code: form.code, storageId: form.sourceStorageId })
    if (res.length) {
      const scannedItem = res[0]
      let length = form.tableData.length
      for (let i = 0; i < length; i++) {
        // console.log(form.tableData[i].currentOutStoreNum, form.tableData[i].currentNum, form.tableData[i].waitDistributionNum);
        if (form.tableData[i].additionalData.some(data => data.uniqueCode === form.code)) {
          message.warning('扫码重复');
        } else {
          if (form.tableData[i].goodsId === scannedItem.goodsId) {
            if (form.tableData[i].currentOutStoreNum >= parseInt(form.tableData[i].currentNum) || form.tableData[i].currentOutStoreNum >= form.tableData[i].waitDistributionNum) {
              message.warning('出库数量不能大于即时库存或待出库数量')
              break
            } else {
              message.success('扫码成功');
            }
            form.tableData[i].additionalData.push(scannedItem);
          }
        }
        // 保存选中的数据返回
        form.tableData[i].checkedKeys = form.tableData[i].additionalData.map(item => item.id)
        form.tableData[i].currentOutStoreNum = form.tableData[i].additionalData.length ? form.tableData[i].additionalData.length : +form.tableData[i].currentOutStoreNum
      }
    }
  } catch (error) {
    console.log(error);
  } finally {
    form.code = '';
  }
}
const getCheckData = (e) => {
  form.tableData.forEach(item => {
    e[0]._value.forEach(v => {
      if (item.goodsId === v.goodsId) {
        item.additionalData = e[0]._value
        item.checkedKeys = e[1]._value
        item.currentOutStoreNum = item.additionalData.length
      }
    })
  });
}
function handleSummary(tableData: Recordable[]) {
  // 金额合计
  const totalAmt = tableData.reduce((prev, next) => {
    console.log(next.selected, 'next.selected');
    if (next.selected) {
      prev += Number(next.currentOutStoreNum * next.goodsPrice);
    }
    return prev;
  }, 0);
  // 数量总计
  const totalNumber = tableData.reduce((prev, next) => {
    if (next.selected) {
      prev += Number(next.currentOutStoreNum);
    }
    return prev;
  }, 0);
  return {
    totalAmount: `总金额 : ${getNumToThousands(totalAmt)}`,
    totalNum: `总数量 : ${totalNumber.toFixed(2)}`,
  };
}
/**
 * 选择事件
 */
const checkedKeys = ref<Array<string | number>>([]);
const checkedRows = ref<Array<string | number>>([]);
const onSelectChange = (selectedRowKeys: (string | number)[], selectionRows) => {
  checkedKeys.value = selectedRowKeys;
  checkedRows.value = selectionRows;
}
const onSelect = (record, selected, selectedRows, nativeEvent) => {
  record.selected = selected
  if (!record.individualFlag) {
    if (parseInt(record.currentNum) > record.waitDistributionNum) {
      record.currentOutStoreNum = record.waitDistributionNum
    } else {
      record.currentOutStoreNum = parseInt(record.currentNum)
    }
  }
}
const onSelectAll = (selected, selectedRows, changeRows) => {
  if (selected) {
    selectedRows.forEach(row => {
      row.selected = selected
      if (!row.individualFlag) {
        if (parseInt(row.currentNum) > row.waitDistributionNum) {
          row.currentOutStoreNum = row.waitDistributionNum
        } else {
          row.currentOutStoreNum = parseInt(row.currentNum)
        }
      }
    })
  } else {
    form.tableData.forEach(row => {
      row.selected = selected
    })
  }
}
const rowSelection = {
  type: 'checkbox',
  columnWidth: 50,
  selectedRowKeys: checkedKeys,
  onChange: onSelectChange,
  onSelect: onSelect,
  onSelectAll: onSelectAll,
  getCheckboxProps(record: Recordable) {
    // Demo: 第一行（id为0）的选择框禁用
    if (record.waitDistributionNum == 0||record.currentNum == 0) {
      return { disabled: true };
    } else {
      return { disabled: false };
    }
  },
};
const handleOk = async () => {
  try {
    let detailList = form.tableData.filter(item => item.selected).map(item => {
      return {
        currentOutStoreNum: item.currentOutStoreNum ? item.currentOutStoreNum : 0,
        goodsCode: item.goodsCode,
        recordIds: item.checkedKeys,
      }
    })
   //遍历detailList，判断item.currentOutStoreNum是否为0，如果有0，直接return 不进行后续逻辑
      if (detailList.some(item => item.currentOutStoreNum == 0)) {
       message.warning('出库数量不能为0，请输入数量后提交');
       return;
     }
    let obj = {
      applyOrderIds: form.applyOrderIds,
      sourceStorageId: form.sourceStorageId,
      targetStorageId: form.targetStorageId,
      pushFlag: +form.pushFlag,
      detailList,
    }
    // console.log(obj, 'obj');
    changeOkLoading(true);
    // console.log('确定', obj);
    await addByApplyOrder(obj)

    //回传选项和已选择的值
    // //关闭弹窗
    closeModal();
    emit('resetRows', []);
    emit('success');
  } catch (e) {
    changeOkLoading(false);
  }
  finally {
    changeOkLoading(false);
  }

}
const rowClassName = computed(() => {
  return (record, index: number) => {
    if( record.waitDistributionNum == 0||record.currentNum == 0){
      return 'grey'
    }
  };
})


</script>
<style lang="scss" scoped>
.ant-table-striped:deep(.grey) td {
  background-color: rgb(224, 224, 224) !important;
}

</style>