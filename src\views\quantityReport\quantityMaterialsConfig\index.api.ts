import { defHttp } from '/@/utils/http/axios';
enum Api {
  list = '/spd/dl/getDlGoods', //查询接口
  addDlType = '/spd/dl/addDlType',
  getDlType = '/spd/dl/getDlType',
  saveOrUpdateDlGoods = '/spd/dl/saveOrUpdateDlGoods', // 新增 & 修改带量物资
  getDlGoodsDetail = '/spd/dl/getDlGoodsDetail', // 带量物资详情
  getXls = '/sys/common/static/template\\%e5%b8%a6%e9%87%8f%e7%89%a9%e8%b5%84%e5%af%bc%e5%85%a5%e6%a8%a1%e6%9d%bf.xls',
  importDlGoods = '/spd/dl/importDlGoods',
  deleteDlData = '/spd/dl/deleteDlData',
}
export const list = (params: any) => defHttp.get({ url: Api.list, params });
export const getDlType = (params?: any) => defHttp.get({ url: Api.getDlType, params });
export const saveOrUpdateDlGoods = (data: any) => defHttp.post({ url: Api.saveOrUpdateDlGoods, data });
export const addDlType = (data: any) => defHttp.post({ url: Api.addDlType, data });
export const getDlGoodsDetail = (params: any) => defHttp.get({ url: Api.getDlGoodsDetail, params });
export const deleteDlGoods = (url: string) => defHttp.delete({ url });
export const getXls = (params?: any) => defHttp.get({ url: Api.getXls, params, responseType: 'blob' }, { isReturnNativeResponse: true }); // 下载excle
export const getImportUrl = Api.importDlGoods;
export const deleteDlData = (params?: string) => defHttp.delete({ url: Api.deleteDlData, params });