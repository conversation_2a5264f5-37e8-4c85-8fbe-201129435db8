<template>
  <div>
    <iframe
      style="display: none"
      id="iframeRef"
      ref="iframeRef"
      :srcdoc="enterContainer"
      frameborder="0"
    ></iframe>
    <div id="print" style="display: none">
      <div v-for="(item, index) in list" :key="item.id" style="page-break-after: always">
        <h1 style="text-align: center;font-size:14px !important">首都医科大学附属北京{{ title }}物资退货单</h1>
        <div class="mt-4" style="text-align: left; width: 100%; display: flex">
          <span style="width: 30%">退货单号: {{ item.returnPurchaseOrderNo }}</span>
          <span style="width: 30%">退货仓库: {{ item.sourceStorageName }}</span>
          <span style="width: 30%">退货时间: {{ item.requestDate }}</span>
        </div>
        <div class="mt-4" style="text-align: left; width: 100%; display: flex">
          <span style="width: 30%">发起人: {{ item.requestUsername }}</span>
          <span style="width: 30%">供应商: {{ item.supplierName }}</span>
          <!-- <span style="width: 28%;">发票号: {{ item.invoiceNo }}</span> -->
        </div>
        <!-- <div class="imgContainers" style="position: absolute; right: -300px; top: -10px">
          <barcode-print
            :key="item + new Date()"
            :value="item.returnPurchaseOrderNo"
            :height="40"
            :displayValue="true"
          >
          </barcode-print>
        </div> -->
        <div style="border: 0.5px solid #474747; width: 100%"></div>
        <a-table
          style="height: 320px"
          :dataSource="item.detailList"
          :columns="columns"
          :pagination="false"
        >
          <template #No="{ record, index }">
            <span style="color: #000">{{ index + 1 }}</span>
          </template>
          <template #goodsSpecsDetail="{ record }">
            <span v-if="getStrLen(record) <= 30" class="td" style="color:#000">{{ getSpecs(record) }}</span>
            <span v-else class="td clamp4" :style="{color:'#000',fontSize:'8px !important'}">{{ getSpecs(record) }}</span>
          </template>
          <template #goodsName="{ record }">
            <span v-if="record.goodsName.length <= 30" class="td" style="color:#000">{{ record.goodsName }}</span>
            <span v-else class="td clamp4" :style="{color:'#000',fontSize:'8px !important'}">{{ record.goodsName }}</span>
          </template>
          <template #batchNo="{ record }">
            <span class="td" style="color: #000">{{ record.batchNo }}</span>
          </template>
        </a-table>
        <div
          v-if="item.totalPage == item.currentPage"
          style="border: 0.5px solid #474747; width: 100%"
        ></div>
        <div class="mt-6" style="display: flex">
          <span style="width: 70%">本页合计</span>
          <span style="width: 15%">数量: {{ item.currentPageTotalNum }}</span>
          <span style="width: 15%">金额: {{ item.currentPageTotalAmt }}</span>
        </div>
        <div v-if="item.totalPage == item.currentPage" class="mt-6" style="display: flex">
          <span style="width: 70%">本单合计</span>
          <span style="width: 15%">数量: {{ item.allPageTotalNum }}</span>
          <span style="width: 15%">金额: {{ item.allPageTotalAmt }}</span>
        </div>
        <div
          v-if="item.totalPage == item.currentPage"
          style="border: 0.5px solid #474747; width: 100%"
        ></div>
        <div
          v-if="item.totalPage != item.currentPage"
          style="border: 0.5px solid #474747; width: 100%"
        ></div>
        <div style="display: flex">
          <span style="width: 30%">退货科室:</span>
          <span style="width: 30%">退货审核人:{{ item.auditUserName }}</span>
          <span style="width: 30%">退货供应商:</span>
        </div>

        <div class="mt-6" style="text-align: center">
          <span>—— {{ item.currentPage }}/{{ item.totalPage }} ——</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch, defineProps, computed, h } from "vue";
import { useUserStore } from "/@/store/modules/user";
import { getSpecs, printStyle, getStrLen } from "/@/utils/index";
const userStore = useUserStore();
let title = ref(userStore.hospitalZoneInfo.comp.name);
const props = defineProps({
  value: {
    type: String,
    default: "",
  },
  list: {
    type: Array,
    default: [],
  },
  masterNo: {
    type: String,
    default: "",
  },
});
const columns = [
  {
    title: "序号",
    slots: { customRender: "No" },
    width: 40,
  },
  {
    title: "材料编码",
    dataIndex: "goodsCode",
    width: 150,
  },
  {
    title: "材料名称",
    dataIndex: "goodsName",
    width: 180,
    slots: { customRender: "goodsName" },
  },
  {
    title: "规格型号",
    dataIndex: "goodsSpecsDetail",
    width: 180,
    slots: { customRender: "goodsSpecsDetail" },
  },
  {
    title: "单位",
    dataIndex: "unitName",
    width: 50,
  },
  {
    title: "退货数量",
    dataIndex: "outStoreDetailNum",
    width: 70,
  },
  {
    title: "单价",
    dataIndex: "goodsPrice",
    width: 60,
  },
  {
    title: "产品批号",
    dataIndex: "batchNo",
    width: 100,
    slots: { customRender: "batchNo" },
  },
  {
    title: "生产日期",
    dataIndex: "productDate",
    width: 90,
  },
  {
    title: "有效日期",
    dataIndex: "term",
    width: 90,
  },
  {
    title: "注册证号",
    dataIndex: "registerNo",
    width: 80,
  },
];
const enterContainer = ref("");
const iframeRef = ref(null);

function print() {
  //赋值
  if (!iframeRef.value) {
    console.error("iframeRef is not set or invalid.");
    return;
  }
  // if (props.list.applyOrderNo && props.list.applyOrderNo.includes(",")) {
  //   props.list.applyOrderNo = ''
  // }
  enterContainer.value = document.getElementById("print").innerHTML;
  // 获取 iframe 内部的文档对象
  const iframeDoc =
    iframeRef.value.contentDocument || iframeRef.value.contentWindow.document;
  // 创建并设置样式
  const style = iframeDoc.createElement("style");
  style.innerHTML = printStyle;
  // 将样式添加到 iframe 内部的文档中的 <head> 元素中
  iframeDoc.head.appendChild(style);
  // 将内容插入到 iframe 中
  iframeDoc.body.innerHTML = enterContainer.value;
  iframeRef.value.contentWindow.print();
}
defineExpose({
  print,
});
</script>
<style scoped lang="scss"></style>
