<template>
  <div>
    <BasicTable bordered rowKey="id" @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-show="checkedKeysBool" @click="selectAll">全选</a-button>
        <a-button type="primary" v-show="!checkedKeysBool" @click="selectAll">清空勾选({{ checkedKeys.length
          }}条数据)</a-button>
        <a-button type="primary" v-auth="'user:addl'" @click="addLowStore">新增代销出库</a-button>
        <a-button type="primary" v-auth="'user:add-high-value-out'" @click="addHighValue">高值代销出库</a-button>
        <a-button type="primary" v-auth="'user:addNew'" @click="addNewStore">新增高值代销出库</a-button>
        <a-button type="primary" v-auth="'user:prod'" @click="produceOrder">生成结算单</a-button>
        <a-button type="primary" v-auth="'user:allprod'" @click="autoproduceOrder">全部生成结算单</a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"> </TableAction>
      </template>
      <template #footer="currentPageData">
        <div class="foot pd6 dpflex jcsb">
          <div>合计</div>
          <div class="dpflex">
            <div class="ml100">{{ `${checkSumAmount}` }}</div>
            <div class="foot-total">{{ getFootTotal(currentPageData) }}</div>
        </div>
      </div>
    </template>
    </BasicTable>
    <DetailModal @register="DetailModals"></DetailModal>
    <NewsModal @register="NewModals" @success="success"></NewsModal>
    <SettleOrderModal @register="SettlesModal" @success="success"></SettleOrderModal>
    <Addmodals @register="Addmodal" @success="success"></Addmodals>
    <NewAddHighValueOutModal @register="NewAddHighValueModal" @success="success"> </NewAddHighValueOutModal>
  </div>
</template>

<script setup lang="ts" name="settlement-saleOutStore">
import { onMounted, ref, computed } from "vue";
import { BasicTable, TableAction } from "/@/components/Table";
import { useListPage } from "/@/hooks/system/useListPage";
import { useModal } from "/@/components/Modal";
import DetailModal from "./components/DetailModal.vue";
import NewsModal from "./components/NewsModal.vue";
import Addmodals from "./components/newConsignmenDelivery/index.vue";
import NewAddHighValueOutModal from "./components/newAddHighValueOut/index.vue";
import SettleOrderModal from "./components/SettleOrderModal.vue";
import { getspdStoragelist } from '/@/api/common/api';
import { list, autoSplitByConsignmentOrder, suBmit,getDeliveryspdStoragelist,audit } from "./saleOutStore.api";
import { searchFormSchema, columns } from "./saleOutStore.data";
import { message } from "ant-design-vue";
import { handleSummaryNew, checkedSum } from '/@/utils/index'
import { useUserStore } from '/@/store/modules/user';
const userStore: any = useUserStore();

const code = (userStore.hospitalZoneInfo?.depart?.orgCode);
const storageId = (userStore.hospitalZoneInfo?.storage?.id);


// const getFootTotal = (currentPageData) => `${handleSummary(currentPageData).goodsNum} ,\xa0` + `${handleSummary(currentPageData).totalAmt}`
const getFootTotal = (currentPageData)=> handleSummaryNew(currentPageData,'totalAmt','consignmentNum')
const checkSumAmount = computed(() => checkedSum(checkedRows.value,'totalAmt','consignmentNum')) // 勾选合计
const [DetailModals, { openModal: Modal }] = useModal(); // 详情modal
const [NewModals, { openModal: NewModal }] = useModal(); //
const [SettlesModal, { openModal: SettleModal }] = useModal(); //
const [Addmodal, { openModal: AddModal }] = useModal(); //
const [NewAddHighValueModal, { openModal: OpenNewAddHighValueModal }] = useModal(); //

const props = defineProps({
  formtype: {
    type: String,
    default: '0'
  }
})

onMounted(() => {
  if (props.formtype == '1') {
    getForm().updateSchema({
      label: '出库仓库',
      field: 'outStorageId',
      component: 'ApiSelect',
      componentProps: {
        api: () => getDeliveryspdStoragelist({ departCode: code }),
        params: {
          //parentId:userStore.getHospitalZoneInfo.storage.id
        },
        showSearch: true,
        optionFilterProp: 'storageNameAbbr',
        labelField: 'storageName',
        valueField: 'id',
      },
      defaultValue: storageId,
      required: true,
    })
  }else{
    getForm().updateSchema({
      label: '出库仓库',
    field: 'outStorageId',
    component: 'ApiSelect',
    componentProps: {
      type: 'like',
      value: [],
      api: () => getspdStoragelist({ column: 'storageType', order: 'asc' }),
      showSearch: true,
      labelField: 'storageName', // label值
      optionFilterProp: 'storageNameAbbr',
      // resultField:'records', //  接口返回的字段，如果接口返回数组，可以不填。支持x.x.x格式
      valueField: 'id', // value值
      // mode: "multiple", // 支持多选
      allowClear: true
    },
    })
  }
});

const checkedKeysBool = ref(true);
let param: any = {};
/**
 * 选择列配置
 */
const checkedKeys = ref<Array<string | number>>([]);
const checkedRows = ref<any>([]);
const onSelectChange = (selectedRowKeys: (string | number)[], selectionRows) => {
  checkedKeys.value = selectedRowKeys;
  checkedRows.value = selectionRows;
  if (checkedKeys.value.length == 0) {
    checkedKeysBool.value = true;
  }
};
const rowSelection = {
  type: "checkbox",
  columnWidth: 50,
  selectedRowKeys: checkedKeys,
  onChange: onSelectChange,
};

const addLowStore = () => {
  //LowModal(true, {});
  AddModal(true, {
    formtype: props.formtype,
  });

};
const addNewStore = () => {
  NewModal(true, {});
};
const addHighValue = () => {
  OpenNewAddHighValueModal(true, {
    formtype: props.formtype,
  });
};
const produceOrder = () => {
  if (!checkedKeys?.value.length) return message.warning("请选择代销出库单");
  SettleModal(true, {
    record: { selectedRows: checkedRows.value, checkedKeys },
  });
};
async function autoproduceOrder() {
  let params = getForm().getFieldsValue();
  params.createTime_begin = getForm().getFieldsValue().createTime_begin
    ? getForm().getFieldsValue().createTime_begin + " 00:00:00"
    : undefined;
  params.createTime_end = getForm().getFieldsValue().createTime_end
    ? getForm().getFieldsValue().createTime_end + " 23:59:59"
    : undefined;
  let obj = await autoSplitByConsignmentOrder(params);
  SettleModal(true, {
    records: obj,
  });
}
const searchInfo = { consignmentType: 1 };
//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    title: "配送单",
    api: list,
    searchInfo,
    columns,
    canResize: false,
    scroll: { y: 480 },
    showIndexColumn: true,
    size: "small",
    formConfig: {
      schemas: searchFormSchema,
      labelCol: { xxl: 8 },
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToNumber: [],
      fieldMapToTime: [
        ["createTime", ["createTime_begin", "createTime_end"], "YYYY-MM-DD"],
      ],
    },
    actionColumn: {
      width: 200,
      fixed: "right",
    },
    beforeFetch(params) {
      param = JSON.parse(JSON.stringify(params));
    },
  },
});
const [registerTable, { reload, clearSelectedRowKeys, getForm }] = tableContext;
const success = () => {
  reload();
  clearSelectedRowKeys();
  console.log("成功");
};
// function handleSummary(tableData) {
//   const totalAmount = tableData.reduce((prev, next) => {
//     prev += Number(next.totalAmt);
//     return prev;
//   }, 0);
//   const totalNumber = tableData.reduce((prev, next) => {
//     prev += Number(next.consignmentNum);
//     return prev;
//   }, 0);
//   return {
//     totalAmt: `本页总金额 : ${getNumToThousands(totalAmount)}`,
//     goodsNum: `本页总数量 : ${totalNumber.toFixed(2)}`,
//   }
// }
/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: "查看",
      onClick: lookDetail.bind(null, record),
    },
    {
      label: "审核",
      popConfirm: {
        title: "是否审核通过",
        okText: "通过",
        confirm: handleAudit.bind(null, record,1),
      },
      ifShow: () => {
        return record.auditStatus === 0 && record.consignmentSource == 1 
      },
    },
    {
      label: "退回",
      popConfirm: {
        title: "是否确认退回",
        confirm: handleAudit.bind(null, record,2),
      },
      ifShow: () => {
        return record.auditStatus === 0 && record.consignmentSource == 1 
      },
    },

  ];
}
const lookDetail = (record: Recordable) => {
  Modal(true, {
    record,
    isUpdate: false,
    showFooter: false,
  });
};
const handleAudit = async (record: Recordable, type) => {
  await audit({ ids: [record.id], auditStatus: type, });
  reload()
};
const selectAll = async () => {
  if (checkedKeysBool.value) {
    console.log(param);
    param.pageSize = -1;
    let res = await list(param)
    checkedKeys.value = res.records.map((item) => item.id);
    checkedRows.value = res.records.map((item) => item);
    checkedKeysBool.value = false;
  } else {
    checkedKeys.value = [];
    checkedRows.value = [];
    checkedKeysBool.value = true;
  }
};

</script>

<style lang="scss" scoped>
</style>
