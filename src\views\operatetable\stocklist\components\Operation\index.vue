<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal2"
    :title="title"
    :width="1500"
    @cancel="getcloseModal"
  >
    <BasicTable @register="registerTable">
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              label: '创建备货单',
              onClick: handleFound.bind(null, record),
            },
          ]"
        />
      </template>
    </BasicTable>
    <!-- 补领 -->
    <ReplenishOrAddModel
      @register="registerModaladd"
      @success="handleSuccess"
    ></ReplenishOrAddModel>
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { BasicModal, useModalInner } from "/@/components/Modal";
import { BasicTable, TableAction } from "/@/components/Table";
import { useModal } from "/@/components/Modal/src/hooks/useModal";
import { useListPage } from "/@/hooks/system/useListPage";
import { formSchema, searchFormSchema } from "./index.data";
import { list } from "./index.api";
import ReplenishOrAddModel from "../../../scheduling/components/ReplenishOrAdd/index.vue";
const [registerModaladd, { openModal: ReplenishOrAddModal }] = useModal();
// Emits声明
const emit = defineEmits(["register", "success"]);
//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    api: list,
    columns: formSchema,
    showIndexColumn: true,
    showTableSetting: false,
    scroll: { y: 300 },
    formConfig: {
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToTime: [
        ["operationTime", ["operationTime_begin", "operationTime_end"], "YYYY-MM-DD"],
      ],
    },
    actionColumn: {
      width: 120,
      fixed: "right",
    },
    // 请求之前对参数做处理
    beforeFetch(params) {
      params.stockStatus = 0;
    },
  },
});

const [registerTable, { reload }] = tableContext;
//表单赋值
const [registerModal2, { setModalProps, closeModal }] = useModalInner(async (data) => {
  reload();
  setModalProps({
    confirmLoading: false,
    showCancelBtn: !!data?.showFooter,
    showOkBtn: !!data?.showFooter,
  });
});

//设置标题
const title = computed(() => "选择手术创建备货单");
/**
 * 创建申领单
 */
async function handleFound(record) {

  ReplenishOrAddModal(true, {
    title: "跟台手术物资备货单",
    record,
    isUpdate: false,
    showFooter: true,
    inventoryStatus: 0,
  });
}

function getcloseModal() {
  //刷新列表
  emit("success");
  closeModal();
}

/**
 * 成功回调
 */
function handleSuccess() {
  reload();
}
</script>

<style lang="less" scoped>
/** 时间和数字输入框样式 */
:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-calendar-picker) {
  width: 100%;
}

</style>
