<template>
  <div id="container">催货管理</div>
  <a-input @keyup.enter="handleChange" v-model:value="value" />
</template>

<script setup lang='ts'>
import { message } from 'ant-design-vue';
import { parseUDI } from 'parse-myudi'
import { onMounted, ref } from 'vue';
const value = ref('')
const handleChange = () => {
  const result = parseUDI(value.value)
  console.log(result, 'result');
  const parsedUI = (result.UDI_GS1_CODE_RESULT.UI)
  console.log(parsedUI, 'parsedUI');
  message.success(`成功${parsedUI}`)
}
// console.log(parseUDI("(01)09450001234009(17)240301(10)ABC123"),'parseMyUDI');


</script>
<style scoped lang='less'></style>