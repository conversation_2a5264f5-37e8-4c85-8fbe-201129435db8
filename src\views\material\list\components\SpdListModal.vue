<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose @ok="handleSubmit" :defaultFullscreen="true"
    :canFullscreen="false" :title="title">
    <div style="padding: 0 4%">
      <BasicForm @register="registerForm">
        <template #medicalFlag="{ model, field }">
          <JDictSelectTag type="select" dictCode='medical_flag' v-model:value="model[field]" />
        </template>
        <template #matCategoryname="{ model, field }">
          <JDictSelectTag type="select" dictCode='category' :disabled="true" v-model:value="model[field]" />
        </template>
        <template #quantitativePackageManagementFlag="{ model, field }" >
          <JDictSelectTag type="select" dictCode='yn' v-model:value="model[field]" :disabled="true"/>
        </template>
        <template #goodsName="{ model, field }">
          <a-input :disabled="false" v-model:value="model[field]" />
        </template>
        <template #registerNo="{ model, field }">
          <a-input :disabled="(registerNo == null || registerNo == '') ? false : true" v-model:value="model[field]" />
        </template>
        <template #measureUnit="{ model, field }">
          <a-input :disabled="false" v-model:value="model[field]" />
        </template>
        <template #goodsPrice="{ model, field }">
          <a-input :disabled="(goodsPrice == null || goodsPrice == '') ? false : true" v-model:value="model[field]" />
        </template>
        <template #manufacturerName="{ model, field }">
          <a-input :disabled="true" v-model:value="model[field]" />
        </template>
        <template #supplierName="{ model, field }">
          <ApiSelect :api="suppliernoPagelist" showSearch v-model:value="model[field]"
            optionFilterProp="supplierNameSupplyPycode" resultField="result" labelField="supplierName" valueField="id"
            placeholder="请选择供应商" allowClear :disabled="(supplierId == null || supplierId == '') ? false : true" />
        </template>
        <template #barcodeFlag="{ model, field }">
          <a-radio-group v-model:value="model[field]">
            <a-radio value="1">是</a-radio>
            <a-radio value="0">否</a-radio>
          </a-radio-group>
        </template>
      </BasicForm>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref, unref } from "vue";
import { BasicModal, useModalInner } from "/@/components/Modal";
import { BasicForm, useForm } from "/@/components/Form/index";
import { editFormSchema } from "../SpdList.data";
import { saveOrUpdate } from "../SpdList.api";
import { JDictSelectTag, ApiSelect } from "/@/components/Form/index";
import { suppliernoPagelist } from '/@/api/common/api'
import { useMessage } from '/@/hooks/web/useMessage';
import { v } from "vxe-table";

const { createMessage, createWarningModal } = useMessage()
// Emits声明
const emit = defineEmits(["register", "success"]);
const isUpdate = ref(true);
const showFooter = ref(true);
const medicalFlag = ref('')
const quantitativePackageFlag = ref('')
const goodsName = ref('')
const registerNo = ref('')
const measureUnit = ref('')
const goodsPrice = ref('')
const supplierId = ref('')
const barcodeFlag = ref('')
//表单赋值
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  data.record.stopFlag = data.record.stopFlag != null ? String(data.record.stopFlag) : '';
  data.record.medicalFlag = data.record.medicalFlag != null ? String(data.record.medicalFlag) : '';
  data.record.matIsSumpur = data.record.matIsSumpur != null ? String(data.record.matIsSumpur) : '';
  data.record.quantitativePackageFlag = data.record.quantitativePackageFlag != null ? String(data.record.quantitativePackageFlag) : '';
  data.record.goodsType = data.record.goodsType != null ? String(data.record.goodsType) : '';
  data.record.purchaseMethod = data.record.purchaseMethod != null ? String(data.record.purchaseMethod) : '';
  data.record.biddingType = data.record.biddingType != null ? String(data.record.biddingType) : '';
  data.record.financeCategory = data.record.financeCategory != null ? String(data.record.financeCategory) : '';
  data.record.goodsNature = data.record.goodsNature != null ? String(data.record.goodsNature) : '';
  data.record.barcodeManagementMode = data.record.barcodeManagementMode != null ? String(data.record.barcodeManagementMode) : '';
  data.record.jdTypeName = data.record.jdTypeName != null ? String(data.record.jdTypeName) : '';
  data.record.registerType = data.record.registerType != null ? String(data.record.registerType) : '';
  data.record.eacName = data.record.eacName != null ? String(data.record.eacName) : '';
  data.record.bulkPurchaseTypeCode = data.record.bulkPurchaseTypeCode != null ? String(data.record.bulkPurchaseTypeCode) : '';
  data.record.barcodeFlag = data.record.barcodeFlag != null ? String(data.record.barcodeFlag) : '';
  medicalFlag.value = ''
  medicalFlag.value = data.record.medicalFlag
  quantitativePackageFlag.value = ''
  quantitativePackageFlag.value = data.record.quantitativePackageFlag
  goodsName.value = ''
  goodsName.value = data.record.goodsName
  registerNo.value = ''
  registerNo.value = data.record.registerNo
  measureUnit.value = ''
  measureUnit.value = data.record.measureUnit
  goodsPrice.value = ''
  goodsPrice.value = data.record.goodsPrice
  supplierId.value = ''
  supplierId.value = data.record.supplierId
  barcodeFlag.value = ''
  barcodeFlag.value = data.record.barcodeFlag


  //重置表单
  await resetFields();
  setModalProps({
    confirmLoading: false,
    showCancelBtn: !!data?.showFooter,
    showOkBtn: !!data?.showFooter,
  });
  isUpdate.value = !!data?.isUpdate;
  showFooter.value = !!data?.showFooter;
  if (unref(isUpdate)) {
    //表单赋值
    await setFieldsValue({
      ...data.record,
    });
  }
  // 隐藏底部时禁用整个表单
  setProps({ disabled: !data?.showFooter });
});
//设置标题
const title = computed(() => "编辑物资信息");


//表单配置
let [registerForm, { setProps, resetFields, setFieldsValue, validate }] = useForm({
  schemas: editFormSchema,
  showActionButtonGroup: false,
  labelCol: {
    span: 6,
  },
  compact: true,
});
//表单提交事件
async function handleSubmit() {
  try {
    let values = await validate();
    setModalProps({ confirmLoading: true });
    if (values.type18MonitorName==null){
      values.type18MonitorName=''
    }
    //提交表单
    await saveOrUpdate(values).then((res) => {
      if (res.data.code === 200) {
        createMessage.success(res?.data?.message);
      } else {
        createMessage.error(res?.data?.message);
      }
    }).catch((err) => {
      console.log(err);
    })


    //关闭弹窗
    closeModal();
    //刷新列表
    emit("success");
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>

<style lang="less" scoped>
/** 时间和数字输入框样式 */
:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-calendar-picker) {
  width: 100%;
}

:deep(.ant-input-affix-wrapper-disabled) {
  color: black;
}

:deep(.ant-divider-plain.ant-divider-with-text) {
  font-size: 24px;
  font-weight: 600;
}

:deep(.ant-col-6) {
  .ant-form-item-label {
    label {
      width: 130px;
      -webkit-box-orient: vertical;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      white-space: wrap;
      min-height: 40px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
}

:deep(.ant-col-24) {
  .ant-col-6 {
    flex: 0 0 10%;
  }
}
</style>
