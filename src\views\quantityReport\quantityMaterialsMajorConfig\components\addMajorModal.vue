<template>
  <BasicModal @register="registerModal" @ok="ok" destroyOnClose>
    <div class="wrapper">
      <div class="out-top">
        <a-form ref="formRef" :model="formData" :rules="rules" layout="inline" :label-col="{ span: 9 }"
          :wrapper-col="{ span: 20 }">
          <div style="width: 100%;height: 100%;display: flex;flex-wrap: wrap;">
            <div class="wrapper-item">
              <div class="w_30">
                <a-form-item label="带量采购分类：" name="dlTypeId" :rules="rules.dlTypeId">
                  <ApiSelect :api="getDlType" v-model:value="formData.dlTypeId" allowClear showSearch
                    placeholder="请选择带量采购分类" optionFilterProp="dlTypeName" labelField="dlTypeName" valueField="id"
                    resultField="result" :filterOption="false" :disabled="type === 'detail'" @change="handleChange" />
                </a-form-item>
              </div>
              <div class="w_30">
                <a-form-item label="专业组" name="departName">
                  <a-input :disabled="type === 'detail'" v-model:value="formData.departName" allowClear
                    placeholder="请输入物资名称" />
                </a-form-item>
              </div>
            </div>
          </div>
          <!-- 物资名称列表 -->
          <div class="wrapper-item" v-for="(domain, index) in formData.dlDoctorList" :key="index">
            <div class="w_30">
              <a-form-item label="物资名称：" :name="['dlDoctorList', index, 'goodsTypeId']">
                <a-select :disabled="type === 'detail'" v-model:value="domain.goodsTypeId" style="width: 100%"
                  :options="options" allowClear placeholder="请选择物资名称">
                </a-select>
              </a-form-item>
            </div>
            <div class="w_30">
              <a-form-item label="任务量" :name="['dlDoctorList', index, 'taskNum']">
                <a-input :disabled="type === 'detail'" v-model:value="domain.taskNum" allowClear placeholder="请输入任务量" />
              </a-form-item>
            </div>
            <div v-if="type !== 'detail'" class="w_30">
              <a-form-item>
                <div class="">
                  <a-button type="link" @click="addRowInp(domain, index)"> {{ '继续添加' }}</a-button>
                  <MinusCircleOutlined v-if="formData.dlDoctorList.length > 1"
                    :disabled="formData.dlDoctorList.length === 1" @click="removeDomain(domain)" />
                  <a-button type="link" @click="handleClear(domain)">清空</a-button>
                </div>
              </a-form-item>
            </div>
          </div>
        </a-form>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, UnwrapRef, reactive, nextTick } from 'vue'
import { MinusCircleOutlined } from '@ant-design/icons-vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { saveOrUpdateDlDepart, getDlDepartGoods, getDlType, getDlGoods } from '../index.api'
import { useMessage } from '/@/hooks/web/useMessage';
import { ApiSelect } from '/@/components/Form/index';
const { createMessage } = useMessage();
const options = ref([])
const handleChange = async (e) => {
  formData.dlDoctorList.forEach(v => {
    v.goodsTypeId = undefined;
  });
  const res = await getDlGoods({ dlTypeId: e,pageSize:'-1' });
  options.value = res.records.map((item) => {
    return {
      label: item.goodsTypeName,
      value: item.id
    }
  })
}
const formRef = ref();
const type = ref('')
const id = ref('')
// rules
const rules = {
  dlTypeId: [
    { required: true, message: '请选择带量采购分类' },
  ],
  departName: [
    { required: true, message: '请输入专业组' },
    { pattern: /^[\u4e00-\u9fa5a-zA-Z]+$/, message: '支持输入中文，英文大小写' }
  ],
}
const $emit = defineEmits(['success'])
const formData: UnwrapRef<any> = reactive({
  dlTypeId: '',
  departName: '',
  departType: '2',
  dlDoctorList: [
    { goodsTypeId: undefined, taskNum: undefined }
  ],
});
const init = () => {
  formData.dlTypeId = null
  formData.departName = null
  formData.dlDoctorList = [
    { goodsTypeId: undefined, taskNum: undefined }
  ]
}
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  type.value = data.type
  setModalProps({ okText: '保存', width: 1400, showCancelBtn: type.value !== 'detail' ? true : false, showOkBtn: type.value !== 'detail' ? true : false, maskClosable: false, keyboard: false });
  init()
  if (type.value !== 'add') {
    id.value = data.record.id
    await loadGoodsDetail(data.record.id);
  }
})
const loadGoodsDetail = async (id) => {
  const res = await getDlDepartGoods({ id });
  handleChange(res.dlTypeId)
  formData.dlTypeId = res.dlTypeId;
  formData.departName = res.departName;
  formData.dlDoctorList = res.dlDoctorList.length ? res.dlDoctorList.map(item => {
    return {
      goodsTypeId: item.goodsTypeId,
      taskNum: item.taskNum
    }
  }) : [
    { goodsTypeId: undefined, taskNum: undefined }
  ]
};
const addRowInp = (domain, index) => {
  formRef.value
    .validate()
    .then(() => {
      formData.dlDoctorList.push({ goodsTypeId: undefined, taskNum: undefined });
      nextTick(() => {
        const wrapper = document.querySelector('.out-top') as HTMLElement;
        if (wrapper) {
          wrapper.scrollTop = wrapper.scrollHeight;
        }
      });
    })
    .catch(error => {
      console.log('校验失败', error);
    });
};
const removeDomain = (item) => {
  let index = formData.dlDoctorList.indexOf(item);
  if (index !== -1) {
    formData.dlDoctorList.splice(index, 1);
  }
};
// 清空
const handleClear = (domain) => {
  for (const key in domain) {
    domain[key] = undefined
  }
}
const ok = () => {
  formRef.value
    .validate()
    .then(async () => {
      setModalProps({ loading: true })
      const filteredList = formData.dlDoctorList.filter(v => v.goodsTypeId || v.taskNum);
      console.log('filteredList', filteredList);
      
      if (type.value === 'add') {
        await saveOrUpdateDlDepart({ ...formData, dlDoctorList: filteredList })
      } else {
        formData.id = id.value
        await saveOrUpdateDlDepart({ ...formData, dlDoctorList: filteredList })
      }
      $emit('success')
      closeModal();
    })
    .catch(error => {
      console.log('error', error);
    }).finally(() => {
      setModalProps({ loading: false })
    });
}
</script>

<style lang="scss" scoped>
.w_30 {
  width: 30%;
  height: 50px;
}

.wrapper {
  display: flex;
  flex-direction: column;
  max-height: 400px;

  .out-top {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    text-align: center;
    height: 50%;
    overflow-y: scroll;

    .wrapper-item {
      width: 100%;
      height: 100%;
      margin-bottom: 10px;
      width: 100%;
      height: 100%;
      display: flex;
      flex-wrap: wrap;
    }
  }
}

.anticon-plus-circle,
.anticon-minus-circle {
  font-size: 20px;
  margin-left: 6px;
}

.ant-form-inline .ant-form-item {
  margin-right: 90px;
}
</style>