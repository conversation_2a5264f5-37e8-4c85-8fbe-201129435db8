<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="register" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd">新建备货单</a-button>
        <a-button type="primary" @click="handlePass">审核通过</a-button>
        <a-button type="primary" @click="handleNoPass" danger>审核不通过</a-button>
        <a-button type="primary" @click="handlePush">推送</a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!-- 详情 -->
    <DetailModel @register="registerModal" @success="handleSuccess"></DetailModel>
    <!-- 补领 -->
    <ReplenishOrAddModel
      @register="registerModaladd"
      @success="handleSuccess"
    ></ReplenishOrAddModel>
    <!-- 新建备货单 -->
    <OperationModel
      @register="registerModalOperation"
      @success="handleSuccess"
    ></OperationModel>
  </div>
</template>

<script lang="ts" name="operatetable-stocklist" setup>
import { BasicTable, TableAction } from "/@/components/Table";
import { useListPage } from "/@/hooks/system/useListPage";
import { columns, searchFormSchema } from "./index.data";
import { list, audit, push, requestReturn } from "./index.api";
import { useModal } from "/@/components/Modal/src/hooks/useModal";
import DetailModel from "./components/Detail/index.vue";
import ReplenishOrAddModel from "../scheduling/components/ReplenishOrAdd/index.vue";
import OperationModel from "./components/Operation/index.vue";
import { message } from "ant-design-vue";

//注册model
const [registerModal, { openModal }] = useModal();
const [registerModaladd, { openModal: ReplenishOrAddModal }] = useModal();
const [registerModalOperation, { openModal: OperationModal }] = useModal();
//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    api: list,
    columns,
    canResize: false,
    scroll:{y:480},
    showIndexColumn: true,
    formConfig: {
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToTime: [
        ["applyOrderTime", ["applyOrderTime_begin", "applyOrderTime_end"], "YYYY-MM-DD"],
        ["operationTime", ["operationTime_begin", "operationTime_end"], "YYYY-MM-DD"],
      ],
    },
    actionColumn: {
      width: 240,
      fixed: "right",
    },
    // 请求之前对参数做处理
    beforeFetch(params) {
      params.beginDate = params.beginDate == null ? null : params.beginDate + " 00:00:00";
      params.endDate = params.endDate == null ? null : params.endDate + " 23:59:59";
    },
  },
});

const [
  register,
  { reload },
  { selectedRowKeys, rowSelection, selectedRows },
] = tableContext;
/**
 * 查看
 */
async function handleDetail(record: Recordable) {
  console.log("详情", record);
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}
/**
 * 审核
 */
async function handleAudit(record: Recordable) {
  console.log("审核", record);
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: true,
  });
}
/**
 * 补领
 */
async function handleReplenish(record: Recordable) {
  console.log("追加备货", record);
  ReplenishOrAddModal(true, {
    title: "跟台手术物资备货单",
    record,
    isUpdate: true,
    showFooter: true,
    inventoryStatus : 1
  });
}
/**
 * 新增
 */
async function handleAdd() {
  OperationModal(true, {
    isUpdate: true,
    showFooter: false,
  });
}
/**
 * 批量审核通过
 */
async function handlePass() {
  let arr: any = [];
  for (let i = 0; i < selectedRows.value.length; i++) {
    if (selectedRows.value[i].auditStatus == 1) {
      for (let j = 0; j < selectedRowKeys.value.length; j++) {
        if (selectedRowKeys.value[j] == selectedRows.value[i].id) {
          arr.push(selectedRows.value[i].patientName);
          selectedRowKeys.value.splice(j, 1);
        }
      }
    }
  }
  if (arr.length > 0) {
    message.warning(arr.toString() + "的备货单已审核通过，不可重复审核");
  }
  let obj = {
    auditStatus: 1,
    ids: selectedRowKeys.value,
  };
  if (selectedRowKeys.value.length > 0) {
    await audit(obj);
    handleSuccess();
  }
}
/**
 * 批量审核不通过
 */
async function handleNoPass() {
  let arr: any = [];
  for (let i = 0; i < selectedRows.value.length; i++) {
    if (selectedRows.value[i].auditStatus == 1) {
      for (let j = 0; j < selectedRowKeys.value.length; j++) {
        if (selectedRowKeys.value[j] == selectedRows.value[i].id) {
          arr.push(selectedRows.value[i].patientName);
          selectedRowKeys.value.splice(j, 1);
        }
      }
    }
  }
  if (arr.length > 0) {
    message.warning(arr.toString() + "的备货单已审核通过，不可重复审核");
  }
  let obj = {
    auditStatus: 2,
    ids: selectedRowKeys.value,
  };
  if (selectedRowKeys.value.length > 0) {
    await audit(obj);
    handleSuccess();
  }
}
/**
 * 批量推送
 */
async function handlePush() {
  await push(selectedRowKeys.value);
  handleSuccess();
}
/**
 * 单个推送
 */
async function handlePushone(record: Recordable) {
  await push([record.id]);
  handleSuccess();
}
/**
 * 退回
 */
async function handleGoBack(record: Recordable) {
  await requestReturn([record.id]);
  handleSuccess();
}
/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}
/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: "查看",
      onClick: handleDetail.bind(null, record),
    },
    {
      label: "编辑备货单",
      onClick: handleReplenish.bind(null, record),
      ifShow: (_action) => {
        return record.auditStatus !== 1; // 根据业务控制是否显示: 非enable状态的不显示启用按钮
      },
    },
    {
      label: "审核",
      onClick: handleAudit.bind(null, record),
      ifShow: (_action) => {
        return record.auditStatus !== 1; // 根据业务控制是否显示: 非enable状态的不显示启用按钮
      },
    },
    {
      label: "退回",
      onClick: handleGoBack.bind(null, record),
      ifShow: (_action) => {
        return record.auditStatus == 1; // 根据业务控制是否显示: 非enable状态的不显示启用按钮
      },
    },
    {
      label: "推送",
      popConfirm: {
        title: "是否确认推送",
        confirm: handlePushone.bind(null, record),
      },
      ifShow: () => {
        return record.pushStatus != "1" && record.auditStatus == 1;
      },
    },
  ];
}
</script>

<style lang="scss" scoped>
</style>
