import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { getspdStoragelist, getDepartListLeaf } from '/@/api/common/api';
import moment, { Moment } from 'moment';
import dayjs from 'dayjs';
const ranges = {
  今天: [moment().startOf("day"), moment()],
  明天: [
    moment().startOf("day"),
    moment().startOf("day").subtract(-1, "days"),
  ],
}
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '物资编码',
    align: 'center',
    dataIndex: 'goodsCode',
    width: 160,
    resizable: true,
    sorter: true,
  },
  {
    title: '物资名称',
    align: 'center',
    dataIndex: 'goodsName',
    width: 160,
    resizable: true,
    sorter: true,
  },
  {
    title: '规格',
    align: 'center',
    dataIndex: 'goodsSpecs',
    resizable: true,
    sorter: true,
    width: 120,
  },
  {
    title: '型号',
    align: 'center',
    dataIndex: 'goodsSpecsDetail',
    resizable: true,
    sorter: true,
    width: 120,
  },
  {
    title: '数量',
    align: 'center',
    dataIndex: 'sendNum',
    resizable: true,
    sorter: true,
    width: 100,
  },
  {
    title: '单价',
    align: 'center',
    dataIndex: 'goodsPrice',
    sorter: true,
    resizable: true,
    width: 120,
  },
  {
    title: '计费单价',
    align: 'center',
    dataIndex: 'hisGoodsPrice',
    sorter: true,
    resizable: true,
    width: 120,
  },
  {
    title: '核对状态',
    align: 'center',
    dataIndex: 'contrastPriceStatus_dictText',
    resizable: true,
    sorter: true,
    width: 120,
  },
  {
    title: '耗材条码',
    align: 'center',
    dataIndex: 'barCode',
    width: 240,
    resizable: true,
    sorter: true,
  },
  {
    title: '手术科室',
    align: 'center',
    dataIndex: 'departName',
    width: 150,
  },
  {
    title: '申领科室',
    align: 'center',
    dataIndex: 'requestDepartName',
    width: 150,
  },
  {
    title: 'UDI',
    align: 'center',
    dataIndex: 'udiCode',
    width: 240,
    resizable: true,
    sorter: true,
  },
  {
    title: '发放日期',
    align: 'center',
    dataIndex: 'sendDate',
    width: 160,
    resizable: true,
    sorter: true,
  },
  {
    title: '业务类型',
    align: 'center',
    dataIndex: 'operationSuppliersStatus_dictText',
    resizable: true,
    width: 120,
    sorter: true,
  },
  {
    title: '出库仓库',
    align: 'center',
    dataIndex: 'storageName',
    resizable: true,
    width: 180,
    sorter: true,
  },
  {
    title: '入库仓库',
    align: 'center',
    dataIndex: 'requestStorageName',
    resizable: true,
    width: 180,
    sorter: true,
  },
  {
    title: '供应商',
    align: 'center',
    dataIndex: 'supplierName',
    resizable: true,
    width: 200,
    sorter: true,
  },
  {
    title: '医嘱医生',
    align: 'center',
    dataIndex: 'doctorName',
    resizable: true,
    width: 120,
    sorter: true,
  },
  {
    title: '患者姓名',
    align: 'center',
    dataIndex: 'patientName',
    resizable: true,
    width: 120,
    sorter: true,
  },
  {
    title: '住院号',
    align: 'center',
    dataIndex: 'liveHospitalCode',
    resizable: true,
    sorter: true,
    width: 160,
  },
];
// //查询数据
// export const searchFormSchema: FormSchema[] = [
//   {
//     label: '发放日期',
//     field: 'sendDate',
//     component: 'RangePicker',
//     componentProps: { valueType: 'Date', ranges: ranges },
//     //默认时间范围是前一个月
//     defaultValue: [dayjs().subtract(1, 'month'), dayjs()],
//   },
//   {
//     label: '耗材条码',
//     field: 'inventoryDetailRecord.uniqueCode',
//     component: 'JInput',
//   },
//   {
//     label: '手术科室',
//     field: 'operationScheduling.departId',
//     component: 'ApiSelect',
//     componentProps: {
//       api: getDepartListLeaf,
//       showSearch: true,
//       optionFilterProp: 'departNameDepartNameAbbr',
//       labelField: 'departName',
//       valueField: 'id',
//       allowClear: true,
//     },
//   },
//   {
//     label: '申领科室',
//     field: 'operationOrder.requestDepartId',
//     component: 'ApiSelect',
//     componentProps: {
//       api: getDepartListLeaf,
//       showSearch: true,
//       optionFilterProp: 'departNameDepartNameAbbr',
//       labelField: 'departName',
//       valueField: 'id',
//       allowClear: true,
//     },
//   },
//   {
//     label: '物资名称',
//     field: 'inventoryDetailRecord.goodsName',
//     component: 'JInput',
//   },
//   {
//     label: '物资编码',
//     field: 'inventoryDetailRecord.goodsCode',
//     component: 'JInput',
//   },
//   {
//     label: '规格',
//     field: 'inventoryDetailRecord.goodsSpecs',
//     component: 'JInput',
//   },
//   {
//     label: '型号',
//     field: 'inventoryDetailRecord.goodsSpecsDetail',
//     component: 'JInput',
//   },
//   {
//     label: '病人姓名',
//     field: 'operationOrder.patientName',
//     component: 'JInput',
//   },
//   {
//     label: '住院号',
//     field: 'operationOrder.liveHospitalCode',
//     component: 'JInput',
//   },
//   {
//     label: "业务类型",
//     field: "operationSuppliersStatus_MultiString",
//     component: "JDictSelectTag",
//     componentProps: {
//       dictCode: "operation_suppliers_status",
//       mode: 'multiple',
//     },
//   },
//   {
//     label: 'UDI',
//     field: 'udiCode',
//     component: 'JInput',
//   },
//   {
//     label: '出库仓库',
//     field: 'operationOrder.storageId',
//     component: 'ApiSelect',
//     componentProps: {
//       api: () => getspdStoragelist({ column: 'storageType', order: 'asc' }),
//       params: {},
//       showSearch: true,
//       optionFilterProp: 'storageNameAbbr',
//       labelField: 'storageName',
//       valueField: 'id',
//     },
//   },
//   {
//     label: '入库仓库',
//     field: 'operationOrder.requestStorageId',
//     component: 'ApiSelect',
//     componentProps: {
//       api: () => getspdStoragelist({ column: 'storageType', order: 'asc' }),
//       params: {},
//       showSearch: true,
//       optionFilterProp: 'storageNameAbbr',
//       labelField: 'storageName',
//       valueField: 'id',
//     },
//   },
//   {
//     label: "核对状态",
//     field: "contrastPriceStatus",
//     component: "JDictSelectTag",
//     componentProps: {
//       dictCode: "charge_check_status",
//     },
//   },
// ];
