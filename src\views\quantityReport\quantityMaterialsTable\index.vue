<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:upload-outlined" @click="handleImportData">导入带量计费数据</a-button>
        <a-button class="ml10" type="primary" @click="handleDelHistory">删除历史带量计费数据</a-button>
      </template>
    </BasicTable>
    <ExportHistoryModal @register="exportHistoryModals" @success="reload()"></ExportHistoryModal>
  </div>
</template>

<script setup lang='ts'>
import { BasicColumn, FormSchema, BasicTable } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage';
import { useModal } from '/@/components/Modal';
import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';
import ExportHistoryModal from './components/ExportHistoryModal.vue';
const { createConfirm } = useMessage();
const [exportHistoryModals, { openModal: openExportHistoryModal }] = useModal();
const list = (params: any) => defHttp.get({ url: '/spd/dl/getDlData', params });
const deleteDlData = (params?: string) => defHttp.delete({ url: '/spd/dl/deleteDlData', params });
const handleImportData = () => {
  openExportHistoryModal(true, {})
}
const handleDelHistory = async () => {
  createConfirm({
    iconType: 'warning',
    title: '删除提示',
    content: '请确认是否删除所有带量计费数据？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteDlData()
      reload()
    },
  });
}
const columns: BasicColumn[] = [
  {
    title: '物资代码',
    align: 'center',
    dataIndex: 'goodsCode',
    width: 120,
  },
  {
    title: '物资名称',
    align: 'center',
    dataIndex: 'goodsName',
    width: 120,
  },
  {
    title: '具体规格',
    align: 'center',
    dataIndex: 'goodsSpecsDetail',
    width: 120,
  },
  {
    title: '批号',
    align: 'center',
    dataIndex: 'batchNo',
    width: 120,
  },
  {
    title: '进价',
    align: 'center',
    dataIndex: 'goodsPrice',
    width: 80,
  },
  {
    title: '高值条码',
    align: 'center',
    dataIndex: 'uniqueCode',
    width: 160,
  },
  {
    title: '单位',
    align: 'center',
    dataIndex: 'unitName',
    width: 80,
  },
  {
    title: '供应商',
    align: 'center',
    dataIndex: 'supplierName',
    width: 120,
  },
  {
    title: '患者姓名',
    align: 'center',
    dataIndex: 'patientName',
    width: 80,
  },
  {
    title: '病案号',
    align: 'center',
    dataIndex: 'liveHospitalCode',
    width: 120,
  },
  {
    title: '医生',
    align: 'center',
    dataIndex: 'doctorName',
    width: 120,
  },
  {
    title: '医嘱日期',
    align: 'center',
    dataIndex: 'billDate',
    width: 120,
  },
  {
    title: '厂商',
    align: 'center',
    dataIndex: 'manufacturerName',
    width: 120,
  },
  {
    title: '售价',
    align: 'center',
    dataIndex: 'salePrice',
    width: 80,
  },
];
const formSchema: FormSchema[] = [
  { field: 'goodsCode', component: 'JInput', label: '物资代码' },
  { field: 'goodsName', component: 'JInput', label: '物资名称' },
  { field: 'supplierName', component: 'JInput', label: '供应商' },
  { field: 'doctorName', component: 'JInput', label: '医生' },
]
const { tableContext, } = useListPage({
  tableProps: {
    api: list,
    columns,
    canResize: false,
    scroll: { y: 480 },
    formConfig: {
      labelCol: {
        xxl: 8
      },
      schemas: formSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToTime: [],
    },
    showIndexColumn: true,
    showActionColumn: false,
  },
})
const [registerTable, { reload }] = tableContext
</script>
<style scoped lang='less'></style>