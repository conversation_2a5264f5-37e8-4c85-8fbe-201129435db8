<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd">新建手术备货</a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!--申领 -->
    <OperationModel @register="registerModal" @success="handleSuccess"></OperationModel>
    <NewOperation @register="registerModalNew" @success="handleSuccess"></NewOperation>

  </div>
</template>

<script lang="ts" name="operatetable-scheduling" setup>
import { BasicTable, TableAction } from "/@/components/Table";
import { useModal } from "/@/components/Modal";
import { useListPage } from "/@/hooks/system/useListPage";
import OperationModel from "./components/ReplenishOrAdd/index.vue";
import NewOperation from "./components/NewOperation/index.vue";
import { columns, searchFormSchema } from "./index.data";
import { list } from "./index.api";

//注册model
const [registerModal, { openModal }] = useModal();
const [registerModalNew, { openModal: openModalNew }] = useModal();
//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    api: list,
    columns,
    canResize: false,
    showIndexColumn: true,
    scroll: { y: 480 },
    size: "small",
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToNumber: [],
      fieldMapToTime: [
        ["operationTime", ["operationTime_begin", "operationTime_end"], "YYYY-MM-DD"],
      ],
    },
    actionColumn: {
      width: 120,
      fixed: "right",
    },
    // 请求之前对参数做处理
    beforeFetch(params) {
      params.scheduleFlag = 1;
    },
  },
});

const [registerTable, { reload }, { selectedRowKeys }] = tableContext;

/**
 * 申领事件
 */
function handleApply(record) {
  console.log(record.stockStatus);
  if (record.stockStatus == 1) {
    openModal(true, {
      title: "跟台手术物资备货单",
      record,
      isUpdate: false,
      showFooter: true,
      inventoryStatus: 1,
    });
  } else if (record.stockStatus == 0) {
    openModal(true, {
      title: "跟台手术物资备货单",
      record,
      isUpdate: false,
      showFooter: true,
      inventoryStatus: 0,
    });
  }
}
/**
 * 新建手术备货事件
 */
function handleAdd(record) {
  openModalNew(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}

/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}
/**
 * 操作栏
 */
function getTableAction(record: any) {
  return [
    {
      label: "手术备货单",
      onClick: handleApply.bind(null, record),
    },
  ];
}
</script>

<style scoped>
</style>
