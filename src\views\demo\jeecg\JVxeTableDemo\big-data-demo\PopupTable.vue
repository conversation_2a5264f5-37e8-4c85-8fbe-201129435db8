<template>
  <teleport to="body">
    <transition name="popup-fade">
      <div v-if="visible" class="popup-overlay" @click="handleOverlayClick">
        <transition name="popup-scale" @after-enter="onAfterEnter">
          <div 
            v-if="visible" 
            ref="popupContainerRef" 
            class="popup-container" 
            :class="popupPosition" 
            @click.stop
            tabindex="0"
            @keydown="handleKeyDown"
          >
            <div class="popup-arrow" :class="arrowPosition"></div>
            <div class="popup-header">
              <span class="popup-title">{{ title }}</span>
              <span class="popup-close" @click="closePopup">×</span>
            </div>
            <div class="popup-content">
              <JVxeTable
                ref="popupTableRef"
                :loading="loading"
                :columns="columns"
                :dataSource="dataSource"
                :maxHeight="500"
                size="small"
                stripe
                rowNumber
                :rowSelection="false"
                :pagination="pagination"
                @pageChange="handlePageChange"
                :rowClassName="getRowClassName"
              >
                <template #myAction="props">
                  <a href="javascript:void(0)" @click="handleAddRow(props.row)">添加</a>
                </template>
              </JVxeTable>
            </div>
          </div>
        </transition>
      </div>
    </transition>
  </teleport>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick, onMounted, onUnmounted } from 'vue';
import { JVxeTable } from '/@/components/jeecg/JVxeTable';
import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createMessage } = useMessage();

/**
 * 弹窗表格组件的属性定义
 * @property {Boolean} visible - 控制弹窗显示/隐藏
 * @property {String} title - 弹窗标题
 * @property {Object} queryParam - 查询参数
 * @property {String} queryUrl - 数据查询接口地址
 * @property {Array} columns - 表格列配置
 * @property {Object} triggerElement - 触发弹窗显示的元素
 */
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '弹窗标题'
  },
  queryParam: {
    type: Object,
    default: () => ({})
  },
  queryUrl: {
    type: String,
    required: true
  },
  columns: {
    type: Array,
    required: true
  },
  triggerElement: {
    type: Object,
    default: null
  }
});

/**
 * 定义组件事件
 * @event update:visible - 更新弹窗显示状态
 * @event select - 选择行数据时触发
 * @event cancel - 取消/关闭弹窗时触发
 */
const emit = defineEmits(['update:visible', 'select', 'cancel']);

const popupTableRef = ref();
const popupContainerRef = ref();
const loading = ref(false);
const dataSource = ref<any[]>([]);
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
});
const popupPosition = ref('');
const arrowPosition = ref('');
const selectedRowIndex = ref(-1); // 新增：跟踪当前选中的行索引

/**
 * 监听查询参数变化
 * 当查询参数变化且弹窗可见时，重置分页到第一页并重新加载数据
 */
watch(() => props.queryParam, () => {
  console.log('queryParam changed:', props.queryParam);
  
  if (props.visible) {
    // 重置分页到第一页
    pagination.value.current = 1;
    loadData();
  }
}, { deep: true });

/**
 * 监听可见性变化
 * 当弹窗变为可见状态时，计算弹窗位置并加载数据
 */
watch(() => props.visible, (newVal) => {
  if (newVal) {
    nextTick(() => {
      calculatePosition();
      // 弹窗显示时聚焦，以便监听键盘事件
      if (popupContainerRef.value) {
        popupContainerRef.value.focus();
      }
    });
    loadData();
    
    // 添加全局键盘事件监听器
    document.addEventListener('keydown', handleGlobalKeyDown);
  } else {
    // 弹窗关闭时移除键盘事件监听器
    document.removeEventListener('keydown', handleGlobalKeyDown);
    // 弹窗关闭时重置选中行
    selectedRowIndex.value = -1;
  }
});

/**
 * 计算弹窗位置
 * 根据触发元素的位置和视口大小，计算弹窗应该显示的位置，确保弹窗不会超出屏幕边界
 */
const calculatePosition = () => {
  if (!props.triggerElement || !popupContainerRef.value) return;
  
  // 等待弹窗渲染完成后再计算位置
  nextTick(() => {
    const triggerRect = props.triggerElement.getBoundingClientRect();
    const popupRect = popupContainerRef.value.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;
    
    // 获取滚动条位置
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
    
    // 计算弹窗在页面中的实际位置
    const popupTop = triggerRect.bottom + scrollTop;
    const spaceBelow = viewportHeight - triggerRect.bottom;
    const spaceAbove = triggerRect.top;
    
    // 计算水平位置，确保不超出屏幕
    let leftPosition = triggerRect.left + triggerRect.width / 2 - popupRect.width / 2 + scrollLeft;
    if (leftPosition < 0) {
      leftPosition = 10;
    } else if (leftPosition + popupRect.width > viewportWidth) {
      leftPosition = viewportWidth - popupRect.width - 10;
    }
    
    // 默认位置在下方
    if (spaceBelow >= popupRect.height + 100) {
      // 放在下方
      popupPosition.value = 'position-bottom';
      arrowPosition.value = 'arrow-top';
      popupContainerRef.value.style.top = `${popupTop + 10}px`;
      popupContainerRef.value.style.left = `${leftPosition+1000}px`;
    } else  {
      // 放在上方
      popupPosition.value = 'position-top';
      arrowPosition.value = 'arrow-bottom';
      popupContainerRef.value.style.top = `${triggerRect.top - popupRect.height + scrollTop}px`;
      popupContainerRef.value.style.left = `${leftPosition+970}px`;
    } 
  });
};

/**
 * 加载数据
 * 根据查询URL和参数获取表格数据
 * @returns {Promise<void>}
 */
const loadData = async () => {
  if (!props.queryUrl) return;
  
  loading.value = true;
  try {
    const params = {
      ...props.queryParam,
      pageNo: pagination.value.current,
      pageSize: pagination.value.pageSize
    };
    
    const res = await defHttp.get({
      url: props.queryUrl,
      params
    });
    
    dataSource.value = res.records || res.data || [];
    pagination.value.total = res.total || 0;
    
    // 数据加载完成后，如果数据不为空，默认选中第一条数据
    if (dataSource.value.length > 0) {
      nextTick(() => {
        selectedRowIndex.value = 0;
      });
    } else {
      selectedRowIndex.value = -1;
    }
  } catch (error) {
    console.error('加载数据失败:', error);
    dataSource.value = [];
    selectedRowIndex.value = -1;
  } finally {
    loading.value = false;
  }
};

/**
 * 处理分页变化
 * 当分页参数改变时更新分页信息并重新加载数据
 * @param {Object} page - 分页信息
 * @param {Number} page.current - 当前页码
 * @param {Number} page.pageSize - 每页条数
 */
const handlePageChange = ({ current, pageSize }) => {
  pagination.value.current = current;
  pagination.value.pageSize = pageSize;
  loadData();
};

/**
 * 处理添加行数据
 * 当用户点击添加按钮时触发select事件，将选中行数据传递给父组件
 * @param {Object} row - 选中的行数据
 */
const handleAddRow = (row) => {
  emit('select', row);
};

/**
 * 关闭弹窗
 * 触发update:visible事件将visible状态设置为false
 */
const closePopup = () => {
  emit('cancel'); // 通知父组件弹窗被取消
  emit('update:visible', false);
};

/**
 * 点击遮罩层关闭弹窗
 * 当用户点击弹窗外部遮罩层时关闭弹窗
 */
const handleOverlayClick = () => {
  closePopup();
};

/**
 * 动画完成后重新计算位置
 * 在弹窗动画完成后重新计算弹窗位置，确保位置正确
 */
const onAfterEnter = () => {
  calculatePosition();
};

/**
 * 处理键盘事件
 * 监听上下箭头键，用于在表格行间导航
 */
const handleKeyDown = (event) => {
  if (loading.value) return;
  
  switch (event.key) {
    case 'ArrowUp':
      event.preventDefault();
      if (selectedRowIndex.value > 0) {
        selectedRowIndex.value--;
      }
      break;
    case 'ArrowDown':
      event.preventDefault();
      if (selectedRowIndex.value < dataSource.value.length - 1) {
        selectedRowIndex.value++;
      }
      break;
    case 'ArrowLeft':
      event.preventDefault();
      // 左箭头键翻到上一页
      if (pagination.value.current > 1) {
        pagination.value.current--;
        loadData();
      } else {
        createMessage.warning('已经是第一页了');
      }
      break;
    case 'ArrowRight':
      event.preventDefault();
      // 右箭头键翻到下一页
      const totalPages = Math.ceil(pagination.value.total / pagination.value.pageSize);
      if (pagination.value.current < totalPages) {
        pagination.value.current++;
        loadData();
      } else {
        createMessage.warning('已经是最后一页了');
      }
      break;
    case 'Enter':
      event.preventDefault();
      // 按回车键选择当前行
      if (selectedRowIndex.value >= 0 && selectedRowIndex.value < dataSource.value.length) {
        handleAddRow(dataSource.value[selectedRowIndex.value]);
      }
      break;
    case 'Escape':
      // 在弹窗内按ESC键关闭弹窗
      event.preventDefault();
      event.stopPropagation();
      closePopup();
      break;
  }
};

/**
 * 处理全局键盘事件（ESC键关闭弹窗）
 */
const handleGlobalKeyDown = (event) => {
  // 按ESC键关闭弹窗
  if (event.key === 'Escape') {
    event.preventDefault();
    closePopup();
  }
};

/**
 * 获取行的类名，用于高亮显示选中的行
 */
const getRowClassName = ({ row, rowIndex }) => {
  if (rowIndex === selectedRowIndex.value) {
    return 'selected-row';
  }
  return '';
};

// 组件卸载时清理事件监听器
onUnmounted(() => {
  document.removeEventListener('keydown', handleGlobalKeyDown);
});

/**
 * 动画完成后重新计算位置
 * 在弹窗动画完成后重新计算弹窗位置，确保位置正确
 */
defineExpose({
  loadData
});
</script>

<style scoped>
/* 淡入淡出动画 */
.popup-fade-enter-active {
  transition: opacity 0.3s ease-out;
}

.popup-fade-leave-active {
  transition: opacity 0.3s ease-in;
}

.popup-fade-enter-from,
.popup-fade-leave-to {
  opacity: 0;
}

/* 缩放动画 */
.popup-scale-enter-active {
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.popup-scale-leave-active {
  transition: all 0.3s cubic-bezier(0.6, -0.28, 0.735, 0.045);
}

.popup-scale-enter-from {
  opacity: 0;
  transform: scale(0.8) translate(-50%, -50%);
}

.popup-scale-enter-to {
  opacity: 1;
  transform: scale(1) translate(-50%, -50%);
}

.popup-scale-leave-from {
  opacity: 1;
  transform: scale(1) translate(-50%, -50%);
}

.popup-scale-leave-to {
  opacity: 0;
  transform: scale(0.8) translate(-50%, -50%);
}

.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.popup-container {
  position: absolute;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 80%;
  height: 600px;
  max-width: 1800px;
  display: flex;
  flex-direction: column;
  z-index: 1001;
  outline: none; /* 移除聚焦时的轮廓 */
}

.popup-container.position-bottom {
  transform: translateX(-50%);
}

.popup-container.position-top {
  transform: translateX(-50%);
}

.popup-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border: 8px solid transparent;
}

.popup-arrow.arrow-top {
  top: -16px;
  left: 50%;
  transform: translateX(-50%);
  border-bottom-color: #fff;
}

.popup-arrow.arrow-bottom {
  bottom: -16px;
  left: 50%;
  transform: translateX(-50%);
  border-top-color: #fff;
}

.popup-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.popup-title {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.popup-close {
  font-size: 24px;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.45);
}

.popup-close:hover {
  color: rgba(0, 0, 0, 0.85);
}
.popup-content {
  padding: 16px 24px;
  overflow: auto;
  flex: 1;
  /* 确保内容区域能够正确填充剩余空间 */
  height: calc(100% - 56px); /* 56px 是 header 的高度 */
}

/* 选中行的样式 */
:deep(.selected-row) {
  background-color: #e6f7ff !important;
}

:deep(.selected-row td) {
  color: #1890ff;
}
</style>