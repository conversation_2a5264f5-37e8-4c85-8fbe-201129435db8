import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { getspdStoragelist, suppliernoPagelist } from '/@/api/common/api';
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '物资编码',
    align: 'center',
    dataIndex: 'goodsCode',
    width:150,
    resizable:true,
  },
  {
    title: '物资名称',
    align: 'center',
    dataIndex: 'goodsName',
    width:150,
    resizable:true,
  },
  {
    title: '收费项目编码',
    align: 'center',
    dataIndex: 'chargeItemsCode',
    width:150,
    resizable:true,
  },
  {
    title: '收费项目名称',
    align: 'center',
    dataIndex: 'chargeItemsName',
    width:150,
    resizable:true,
  },
  {
    title: '收费价格',
    align: 'center',
    dataIndex: 'chargePrice',
    width:150,
    resizable:true,
  },
  {
    title: '是否高值',
    align: 'center',
    dataIndex: 'expensiveFlag_dictText',
    width:80,
    resizable:true,
  },
  {
    title: '项目状态',
    align: 'center',
    dataIndex: 'itemsStatus_dictText',
    width:120,
    resizable:true,
  },
  {
    title: '金额是否一致',
    align: 'center',
    dataIndex: 'priceFlag_dictText',
    width:120,
    resizable:true,
  },
  {
    title: '财务分类',
    align: 'center',
    dataIndex: 'financeCategory_dictText',
    width:80,
    resizable:true,
  },
  {
    title: '是否绑定关联关系',
    align: 'center',
    dataIndex: 'bindingFlag_dictText',
    width:140,
    resizable:true,
  },
  {
    title: '院区',
    align: 'center',
    dataIndex: 'hospitalZoneName',
    width:150,
    resizable:true,
  },
  {
    title: '供应商',
    align: 'center',
    dataIndex: 'supplierName',
    width:150,
    resizable:true,
  },
  {
    title: '单位',
    align: 'center',
    dataIndex: 'packageUnit',
    width:80,
    resizable:true,
  },
  {
    title: '单价',
    align: 'center',
    dataIndex: 'goodsPrice',
    width:80,
    resizable:true,
  },
  {
    title: '规格',
    align: 'center',
    dataIndex: 'goodsSpecs',
    width:150,
    resizable:true,
  },
  {
    title: '型号',
    align: 'center',
    dataIndex: 'goodsSpecsDetail',
    width:150,
    resizable:true,
  },
  {
    title: '注册证号',
    align: 'center',
    dataIndex: 'registerNo',
    width:220,
    resizable:true,
  },
  {
    title: '生产厂商',
    align: 'center',
    dataIndex: 'manufacturerName',
    width:150,
    resizable:true,
  },
  {
    title: '创建时间',
    align: 'center',
    dataIndex: 'createTime',
    width:120,
    resizable:true,
  },
  {
    title: '创建人',
    align: 'center',
    dataIndex: 'createRealname',
    width:80,
    resizable:true,
  },
  {
    title: '修改时间',
    align: 'center',
    dataIndex: 'updateTime',
    width:120,
    resizable:true,
  },
  {
    title: '修改人',
    align: 'center',
    dataIndex: 'updateRealname',
    width:80,
    resizable:true,
  },
  {
    title: '同步时间',
    align: 'center',
    dataIndex: 'syncTime',
    width:80,
    resizable:true,
  },
  {
    title: '同步人',
    align: 'center',
    dataIndex: 'syncRealname',
    width:80,
    resizable:true,
  },
  {
    title: '是否同步his',
    align: 'center',
    dataIndex: 'syncStatus_dictText',
    width:120,
    resizable:true,
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '收费项目',
    field: 'chargeItems',
    component: 'Input',
  },
  {
    label: '注册证号',
    field: 'registerNo',
    component: 'Input',
  },
  {
    label: '物资编码',
    field: 'goodsCode',
    component: 'Input',
  },
  {
    label: '物资名称',
    field: 'goodsName',
    component: 'Input',
  },
  {
    label: '供应商',
    field: 'supplierId',
    component: 'ApiSelect',
    componentProps: {
      api: suppliernoPagelist,
      labelField: 'supplierName',
      valueField: 'id',
      showSearch: true,
      optionFilterProp: "supplierNameSupplyPycode",
    },
  },
  {
    label: '生产厂商',
    field: 'manufacturerName',
    component: 'Input',
  },
  {
    field: 'itemsStatus',
    component: 'JDictSelectTag',
    label: '项目状态',
    componentProps: () => {
      return {
        dictCode: 'charge_items_status',
      };
    },
  },
  {
    field: 'chargeFlag',
    label: '是否收费',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'yn',
      };
    },
  },
  {
    field: 'expensiveFlag',
    label: '是否高值',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'yn',
      };
    },
  },
  {
    field: 'syncStatus',
    label: '同步状态',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'activiti_sync',
      };
    },
  },
  {
    label: '创建时间',
    field: 'createTime',
    component: 'RangePicker',
    componentProps: { valueType: 'Date'},
  },
  {
    label: '修改时间',
    field: 'updateTime',  
    component: 'RangePicker',
    componentProps: { valueType: 'Date'},
  },
  {
    label: '财务分类',
    field: 'financeCategory',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'db_finance_category',
      };
    },
  },
  {
    label: '是否绑定关联关系',
    field: 'bindingFlag',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'yn',
      };
    },
  },
  {
    label: '金额是否一致',
    field: 'priceFlag',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'yn',
      };
    },
  },
];
