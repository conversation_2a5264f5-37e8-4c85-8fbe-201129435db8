<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <SpdReturnstoreDrawer @register="registerDrawer" @success="handleSuccess"></SpdReturnstoreDrawer>
  </div>
</template>

<script lang="ts" setup name="storeInout-returnStoreTransfer">
import { BasicTable, TableAction } from '/@/components/Table';
import { useDrawer } from '/@/components/Drawer';
import { useListPage } from '/@/hooks/system/useListPage';
import SpdReturnstoreDrawer from './components/SpdReturnstoreDrawer.vue';
import { columns, searchFormSchema } from './SpdReturnStoreTransfer.data';
import { list, confirmReturnOrder } from './SpdReturnStoreTransfer.api';


//注册drawer
const [registerDrawer, { openDrawer }] = useDrawer();
//注册table数据
const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
  tableProps: {
    api: list,
    columns,
    canResize: false,
    scroll:{y:480},
    immediate:false,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToNumber: [
      ],
      fieldMapToTime: [
      ],
    },
    actionColumn: {
      width: 150,
      fixed: 'right'
    },
    // 请求之前对参数做处理
    beforeFetch(params) {
      //params.checkStatus= 1;
    },
  },
})

const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext
/**
  * 查看
 */
function handleDetail(record: Recordable) {
  openDrawer(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}
/**
  * 确认
 */
async function handleEnit(record: Recordable) {
  console.log('确认', record);
  if (!record.acceptUser) {
    await confirmReturnOrder({ returnId: record.id })
    reload()
  }
  reload()
}


/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}
/**
   * 操作栏
   */
function getTableAction(record) {
  return [
    {
      label: '查看',
      onClick: handleDetail.bind(null, record),
    },
    {
      label: '确认',
      popConfirm: {
        title: '是否确认退货单',
        confirm: handleEnit.bind(null, record),
      },
      ifShow: (_action) => {
                return record.acceptUser == null; // 根据业务控制是否显示: 非enable状态的不显示启用按钮
            },
    },
  ]
}
</script>

<style scoped>
</style>