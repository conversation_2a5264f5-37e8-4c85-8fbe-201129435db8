<template>
  <div>
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:upload-outlined" @click="handleImportData">导入计费数据</a-button>
        <a-button class="ml10" type="primary" @click="handleDeCreate">生成对账数据</a-button>
        <a-button class="ml10" type="primary" @click="handlePush">推送对账数据</a-button>
        <a-button class="ml10" type="primary" @click="handleExportExcel">导出</a-button>
      </template>
      <template #footer>
        <div class="foot pd6 dpflex jcsa">
          <div style="width: 40%;">合计</div>
          <div style="width: 60%;font-weight: 600;" class="dpflex jcse">
            <span>上月结存总数：{{ lastMonthNum }}</span>
            <span>配送总数：{{ deliveryNum }}</span>
            <span>计费总数：{{ billNum }}</span>
            <span>退货总数：{{ returnNum }}</span>
            <span>当月结存总数：{{ lastNum }}</span>
            <span>异常总数：{{ errorNum }}</span>
          </div>
        </div>
      </template>
    </BasicTable>
    <ExportModal @register="ExportModals" @success="handleSuccess"></ExportModal>
  </div>
</template>

<script setup lang='ts'>
import { BasicTable } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage';
import { columns, formSchema } from './index.data';
import { list, push, build, queryAccount, exportExcel } from './index.api';
import { exportFile } from '/@/utils/index'
import ExportModal from './components/ExportModal.vue'
import { useModal } from '/@/components/Modal';
const [ExportModals, { openModal: openExportModal }] = useModal();
import { useMessage } from '/@/hooks/web/useMessage';
import { ref } from 'vue';
const lastMonthNum = ref('')
const deliveryNum = ref('')
const billNum = ref('')
const returnNum = ref('')
const lastNum = ref('')
const errorNum = ref('')
const { createConfirm, createMessage } = useMessage();
const handleSuccess = () => {
  reload()
  clearSelectedRowKeys()
}
const handleImportData = () => {
  openExportModal(true, {});
}
const handleDeCreate = async () => {
  createConfirm({
    iconType: 'warning',
    title: '生成对账数据',
    content: '是否确认生成对账数据？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await build()
      handleSuccess()
    },
  });
}
const handlePush = async () => {
  if (!selectedRowKeys.value.length) return createMessage.error('请选择需要推送的数据!');
  createConfirm({
    iconType: 'warning',
    title: '推送对账数据',
    content: '是否确定推送？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await push({ ids: selectedRowKeys.value })
      handleSuccess()
    },
  });
}
const handleExportExcel = async () => {
  try {
    setLoading(true)
    let params = getForm().getFieldsValue()
    const res = await exportExcel(params)
    exportFile(res)
  } finally {
    setLoading(false)
  }
}
const { tableContext, } = useListPage({
  tableProps: {
    api: list,
    columns,
    canResize: false,
    scroll: { y: 480 },
    formConfig: {
      labelCol: {
        xxl: 8
      },
      schemas: formSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToTime: [],
    },
    beforeFetch(params) {
      if (params.month && typeof params.month === 'object' && params.month.$isDayjsObject) {
        params.month = params.month.format('YYYY-MM');
      }
    },
    async afterFetch(params) {
      try {
        const formData = getForm().getFieldsValue();
        if (formData.month && typeof formData.month === 'object' && formData.month.$isDayjsObject) {
          formData.month = formData.month.format('YYYY-MM');
        }
        const res = await queryAccount(formData)
        const { lastMonthNum: resLastMonthNum, deliveryNum: resDeliveryNum, billNum: resBillNum, returnNum: resReturnNum, lastNum: resLastNum, errorNum: resErrorNum } = res
        lastMonthNum.value = resLastMonthNum
        deliveryNum.value = resDeliveryNum
        billNum.value = resBillNum
        returnNum.value = resReturnNum
        lastNum.value = resLastNum
        errorNum.value = resErrorNum
      } catch (e) {
        console.log(e, 'e');
      }
    },
    showIndexColumn: true,
    showActionColumn: false,
  },
})
const [registerTable, { reload, clearSelectedRowKeys, getForm, setLoading }, { rowSelection, selectedRowKeys }] = tableContext
</script>
<style scoped lang='less'></style>
