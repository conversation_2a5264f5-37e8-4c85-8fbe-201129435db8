<template>
  <a-modal v-model:visible="visible" :keyboard="false" title="消息通知" width="800px" :maskClosable="false"
    :closable="false" @update:visible="handleVisibleChange">
    <a-spin :indicator="indicator" :spinning="loading">
      <div class="outer" ref="containerRef">
        <div class="content" style="margin-bottom: 10px;font-weight: 600;" v-for="val in messageList" :key="val.id">
          有未计费耗材，{{ val.requestDepartName }}, 患者名称：{{ val.patientName }}, 病历号：{{ val.liveHospitalCode }}, 医生：{{
            val.doctorName }}, {{ '请尽快计费' }}
        </div>
        <div v-if="!hasMore" class="no-more-text">没有更多数据了</div>
      </div>
    </a-spin>
    <template #footer>
      <a-button type="primary" @click="route.go(0)">确认</a-button>
    </template>
  </a-modal>
</template>

<script setup lang='ts'>
import { ref, onMounted, onUnmounted, nextTick, h } from 'vue';
import { LoadingOutlined } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import { indexOperationChargeStatus } from "./index.api";
import { useThrottleFn } from '@vueuse/core';

const route = useRouter();
const visible = ref(false);
const containerRef = ref<HTMLElement | null>(null);
const pageNo = ref(1);
const pageSize = ref(10);
const loading = ref(false);
const hasMore = ref(true);
const messageList = ref<any[]>([]);

const indicator = h(LoadingOutlined, {
  style: {
    fontSize: '24px',
  },
  spin: true,
});

// 监听弹窗关闭
const handleVisibleChange = (val: boolean) => {
  if (!val) {
    if (containerRef.value) {
      containerRef.value.removeEventListener('scroll', handleScroll);
    }
  }
};

const fetchData = async () => {
  if (loading.value || !hasMore.value) return;

  loading.value = true;
  try {
    const res = await indexOperationChargeStatus({
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      departId: departId.value
    });

    if (res?.result?.records && Array.isArray(res?.result?.records)) {

      messageList.value = [...messageList.value, ...res?.result?.records];

      // 是否加载更多
      hasMore.value = res?.result?.records.length === pageSize.value;

      if (!hasMore.value) {
      }

      pageNo.value++;
    } else {
      hasMore.value = false;
    }
  } finally {
    loading.value = false;
  }
};


const handleScrollOriginal = async () => {

  if (!containerRef.value) return;

  if (!hasMore.value) return;

  const { scrollTop, scrollHeight, clientHeight } = containerRef.value;
  // 距离底部120px
  if (scrollHeight - scrollTop - clientHeight < 120) {
    ;
    await fetchData();
  }
};

// 使用节流包装的滚动处理函数
const handleScroll = useThrottleFn(handleScrollOriginal, 500);

const departId = ref()
const show = async (data) => {
  departId.value = data
  visible.value = true;
  messageList.value = [];
  pageNo.value = 1;
  hasMore.value = true;
  await fetchData();

  await nextTick(() => {
    if (containerRef.value) {
      containerRef.value.addEventListener('scroll', handleScroll, { passive: true });
    }
  });
};


onMounted(() => {

});

onUnmounted(() => {
  if (containerRef.value) {
    containerRef.value.removeEventListener('scroll', handleScroll);
  }
});

defineExpose({
  show,
});
</script>

<style scoped lang='less'>
.outer {
  height: 300px;
  margin: 10px 0;
  overflow-y: auto;
  position: relative;

  .content {
    margin-left: 50px;
  }

  .loading-text,
  .no-more-text {
    text-align: center;
    padding: 10px 0;
    color: #999;
    font-size: 14px;
  }
}
</style>
