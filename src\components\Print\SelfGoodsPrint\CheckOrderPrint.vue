<template>
  <div>
    <iframe style="display: none" ref="iframeRef" :srcdoc="checkOrderPrintRef" frameborder="0"></iframe>
    <div id="print" style="display: none">
      <div v-for="(item, index) in props.list" :key="item.id" style="page-break-after:always;">
        <h1 style="text-align: center;font-size: 14px;">首都医科大学附属北京{{ title }}物资验收单</h1>
        <div class="mt-6" style=" text-align: left;width: 100%;display: flex; ">
          <span style="width: 40%;">验收单号: {{ item.deliveryOrderNo }}</span>
          <span style="width: 30%;">验收日期: {{ item.checkDateTime }}</span>
          <span style="width: 30%;">供应商: {{ item.supplierName }}</span>
        </div>
        <div class="mt-6" style="text-align: left;width: 100%;display: flex;">
          <span style="width: 40%;">申领科室: {{ item.applyDepartName }}</span>
          <span style="width: 30%;">响应仓库: {{ item.purchaseStorageName }}</span>
          <span style="width: 30%;">核算类型: {{ item.fundSource }}</span>
        </div>
        <div class="mt-6" style="text-align: left;width: 100%;display: flex;">
          <div style="width: 100%;">送货类型: {{ item.settlementType_dictText }}</div>
        </div>
        <div style="border: 0.5px solid #474747;width: 100%;"></div>
        <a-table style="height: 530px;" :dataSource="item.detailList" :columns="columns" :pagination="false">
          <template #goodsSpecsDetail="{ record }">
            <span v-if="getStrLen(record) <= 30" class="td" style="color:#000">{{ getSpecs(record) }}</span>
            <span v-else class="td clamp4" :style="{color:'#000',fontSize:'8px !important'}">{{ getSpecs(record) }}</span>
          </template>
          <template #goodsName="{ record }">
            <span v-if="record.goodsName.length <= 30" class="td" style="color:#000">{{ record.goodsName }}</span>
            <span v-else class="td clamp4" :style="{color:'#000',fontSize:'8px !important'}">{{ record.goodsName }}</span>
          </template>
        </a-table>
        <div style="border: 0.5px solid #474747;width: 100%;"></div>
        <div class="mt-6" style="display: flex;">
          <span style="width: 55%;">本页合计</span>
          <span style="width: 15%;">金额: {{ item.currentPageTotalAmt }}</span>
          <span style="width: 15%;">送货数量: {{ item.currentPageTotalNum }}</span>
          <span style="width: 15%;">验收数量: {{ item.currentPageTotalNum }}</span>
        </div>
        <div class="mt-6" style="display: flex;">
          <span style="width: 55%;">本单合计</span>
          <span style="width: 15%;">金额: {{ item.allPageTotalAmt }}</span>
          <span style="width: 15%;">送货数量: {{ item.allPageTotalNum }}</span>
          <span style="width: 15%;">验收数量: {{ item.allPageTotalNum }}</span>
        </div>
        <div style="border: 0.5px solid #474747;width: 100%;"></div>
        <div class="mt-4" style="display: flex;">
          <span style="width: 33%;">验收人:{{ item.checkUser }}</span>
          <span style="width: 33%;">采购员:{{ item.purchaseUser }}</span>
        </div>
        <div style="border: 0.5px solid #474747;width: 100%;"></div>
        <div class="mt-6" style="text-align: center; ">
          <span>—— {{ item.currentPage }}/{{ item.totalPage }} ——</span>
        </div>
      </div>
    </div>
  </div>
</template>
 
<script setup >
import { ref, onMounted, nextTick, watch, defineProps } from 'vue';
import { useUserStore } from '/@/store/modules/user';
import { getSpecs, printStyle, getStrLen } from '/@/utils/index';
const userStore = useUserStore()
let title = ref(userStore.hospitalZoneInfo.comp.name)
const props = defineProps({
  list: {
    type: Array,
    default: [],
  },
  columns: {
    type: Array,
    default: [],
  },
});
const checkOrderPrintRef = ref('');
const iframeRef = ref(null);

function print() {
  if (!iframeRef.value) {
    console.error("iframeRef is not set or invalid.");
    return;
  }
  checkOrderPrintRef.value = document.getElementById('print').innerHTML;
  const iframeDoc = iframeRef.value.contentDocument || iframeRef.value.contentWindow.document;
  const style = iframeDoc.createElement('style');
  // 默认A4纸横向打印
  const a4PrintStyle = `
    @page {
      size: A4 landscape;
      margin: 10mm;
    }
    @media print {
      body {
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
      }
    }
  `;
  
  style.innerHTML = printStyle + a4PrintStyle;
  iframeDoc.head.appendChild(style);
  iframeDoc.body.innerHTML = checkOrderPrintRef.value;
  iframeRef.value.contentWindow.print();
}
defineExpose({
  print,
});
</script>
<style scoped lang="scss"></style>
