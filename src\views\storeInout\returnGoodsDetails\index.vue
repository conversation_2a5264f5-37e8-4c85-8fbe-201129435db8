<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="getExportXls" :loading="loading"
          v-auth="'store-return-goods-details-export'">
          导出</a-button>
      </template>
      <template #code="{ record }">
        <span style="color: #33a3dc; cursor: pointer" @click="getOpenModel(record)">
          {{ record.returnPurchaseOrderNo }}
        </span>
      </template>
      <template #footer="currentPageData">
        <div class="foot pd6 dpflex jcsb">
          <div>合计</div>
          <div class="foot-total">{{ getFootTotal(currentPageData) }}</div>
        </div>
      </template>
    </BasicTable>
    <InventoryDetailsModal @register="registerModel" @success="handleSuccess"></InventoryDetailsModal>
  </div>
</template>
<script lang="ts" name="storeInout-InventoryDetails" setup>
import { BasicTable } from "/@/components/Table";
import { useModal } from "/@/components/Modal";
import { useListPage } from "/@/hooks/system/useListPage";
import { columns, searchFormSchema } from "./index.data";
import { list, getExportUrl } from "./index.api";
import InventoryDetailsModal from "./components/InventoryDetailsModal.vue";
import { getNumToThousands, exportFile } from "/@/utils/index";
import { ref } from "vue";

const getFootTotal = (currentPageData) =>
  `${handleSummary(currentPageData).totalNum} ,\xa0` +
  `${handleSummary(currentPageData).totalAmount}`;
//注册drawer
const [registerModel, { openModal }] = useModal();

//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    api: list,
    columns,
    rowKey: 'id',
    canResize: false,
    scroll: { y: 400 },
    showIndexColumn: true,
    ellipsis: true,
    immediate:false,
    indexColumnProps: {
      fixed: "left",
    },
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToNumber: [],
      fieldMapToTime: [
        ["returnPurchaseOrder.requestDate", ["beginDate", "endDate"], "YYYY-MM-DD"],
      ],
    },
    showActionColumn: false,
    beforeFetch(params) {
      params.beginDate =
        params.beginDate == null ? null : params.beginDate + " 00:00:00";
      params.endDate =
        params.endDate == null ? null : params.endDate + " 23:59:59";

      if (params.returnPurchaseOrder) {
        params.returnPurchaseOrder = null;
      }
    },
  },
});
const [registerTable, { reload, getForm }, { rowSelection }] = tableContext;

function handleSummary(tableData: Recordable[]) {
  // 金额合计
  const totalAmt = tableData.reduce((prev, next) => {
    prev += Number(next.totalAmt);
    return prev;
  }, 0);
  // 数量总计
  const totalNumber = tableData.reduce((prev, next) => {
    prev += Number(next.totalNum);
    return prev;
  }, 0);
  return {
    totalAmount: `本页总金额 : ${getNumToThousands(totalAmt)}`,
    totalNum: `本页总数量 : ${totalNumber.toFixed(2)}`,
  };
}

const onExportXls = async () => {
  let obj = getForm().getFieldsValue();
  for (let key in obj) {
    if (typeof obj[key] == "object") {
      for (let str in obj[key]) {
        obj[key + "." + str] = obj[key][str];
      }
    }
  }

  if (obj.returnPurchaseOrder?.requestDate) {
    let begin = timestampToDateFormat(obj.returnPurchaseOrder.requestDate[0]);
    if (begin == null) {
      obj.beginDate = null;
    } else {
      obj.beginDate = begin + ' 00:00:00';
    }
    let end = timestampToDateFormat(obj.returnPurchaseOrder.requestDate[1]);
    if (end == null) {
      obj.endDate = null;
    } else {
      obj.endDate = end + ' 23:59:59';
      delete obj['returnPurchaseOrder.requestDate'];
    }
  } 
  delete obj.returnPurchaseOrder;


  await getExportUrl(obj).then(res => {
    exportFile(res);
  })
};


function timestampToDateFormat(timestamp) {
  const dateObj = new Date(timestamp); // 创建Date对象
  const year = dateObj.getFullYear(); // 获取年份
  const month = ("0" + (dateObj.getMonth() + 1)).slice(-2); // 获取月份，并补零
  const day = ("0" + dateObj.getDate()).slice(-2); // 获取日期，并补零
  
  return `${year}-${month}-${day}`; // 返回转换后的日期格式
}

const loading = ref(false);
const getExportXls = async () => {
  loading.value = true;
  await onExportXls();
  loading.value = false;
};


function handleSuccess() {
  reload();
}
/**
 * 弹窗弹出
 */
const getOpenModel = (record) => {
  openModal(true, {
    record,
    isUpdate: false,
    showFooter: false,
  });
};
</script>

<style scoped></style>
