import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/spd/verificationChannelRecord/list',
  getspdStoragelist = '/spd/spdStorage/allList',//仓库列表
}
/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });
export const getspdStoragelist = async (params,) => {
  const res = await defHttp.get({ url: Api.getspdStoragelist, params: params })
  res.forEach(item => {
    item.storageNameAbbr = item.storageName + item.simpleCode;
  });
  return res;
};