
import { BasicColumn, FormSchema } from '/@/components/Table';
import { FormatNumToThousands } from '/@/utils/index'

export const searchFormSchema: FormSchema[] = [
  {
    label: '出库单号',
    field: 'outStoreNo',
    component: 'JInput',
  },
 
  {
    field: 'pickUpDate',
    component: 'RangePicker',
    componentProps: { valueType: 'Date'},
    label: '生成日期',
  },
];
export const columns: BasicColumn[] = [
  {
    title: '科室出库单号',
    align: 'center',
    dataIndex: 'outStoreNo',width:180,resizable:true,
  },
  {
    title: '出库科室',
    align: 'center',
    dataIndex: 'departName',width:200,resizable:true,
  },
  {
    title: '生成日期',
    align: 'center',
    dataIndex: 'pickUpDate',width:180,resizable:true,
  },
  {
    title: '响应仓库',
    align: 'center',
    dataIndex: 'sourceStorageName',width:200,resizable:true,
  },
  {
    title: '总数量',
    align: 'center',
    dataIndex: 'outStoreNum',width:120,resizable:true,
  },
  {
    title: '总金额（元）',
    align: 'center',
    dataIndex: 'totalAmt',width:120,resizable:true,
    customRender: ({ text }) => {
      return FormatNumToThousands(text);
    },
  },
  {
    title: '出库单状态',
    align: 'center',
    dataIndex: 'outStoreRevokeStatus_dictText',width:120,resizable:true,
  },
];
export const lowColumns: BasicColumn[] = [
  {
    title: '物资编号',
    align: 'center',
    dataIndex: 'goodsCode',
    sorter:true,
    width: 130,
  },
  {
    title: '物资名称',
    align: 'center',
    dataIndex: 'goodsName',
    width: 200,
  },
  {
    title: '唯一码',
    align: 'center',
    dataIndex: 'batchCode',
    width: 240,
  },
  {
    title: '规格',
    align: 'center',
    dataIndex: 'goodsSpecs',
    width: 120,
  },
  {
    title: '型号',
    align: 'center',
    dataIndex: 'goodsSpecsDetail',
    width: 120,
  },
  {
    title: '当前库存',
    align: 'center',
    dataIndex: 'totalNum',
     width: 100,
  },
  {
    title: '出库数量',
    align: 'center',
    dataIndex: 'currentOutStoreNum',
    slots: { customRender: 'currentOutStoreNum' },
     width: 100,
  },
  //{
  //  title: '是否定数包',
  //  align: 'center',
  //  dataIndex: 'quantitativePackageFlag_dictText',
  //},
  {
    title: '效期',
    align: 'center',
    dataIndex: 'term',
    width:150,
    sorter:true,
  },
  {
    title: '单位',
    align: 'center',
    dataIndex: 'unitName',
    width: 80,
  },
  {
    title: '批号',
    align: 'center',
    dataIndex: 'batchNo',
    width: 120,
  },
  {
    title: '单价',
    align: 'center',
    dataIndex: 'goodsPrice',
    sorter:true,
    width: 120,
  },
  {
    title: '金额',
    align: 'center',
    dataIndex: 'billTotalAmt',
    slots: { customRender: 'billTotalAmt' },
    width: 120,

  },
  {
    title: '品牌',
    align: 'center',
    dataIndex: 'brand',
    width: 150,
  },
  {
    title: '生产厂商',
    align: 'center',
    dataIndex: 'manufacturerName',
  },
];
export const lowPackagesColumns: BasicColumn[] = [
  {
    title: '物资编号',
    align: 'center',
    dataIndex: 'goodsCode',
    sorter:true,
    width: 140,
  },
  {
    title: '物资名称',
    align: 'center',
    dataIndex: 'goodsName',
    width: 200,
  },
  {
    title: '条码',
    align: 'center',
    dataIndex: 'batchCode',
    width:240,
  },
  {
    title: '定数包规格',
    align: 'center',
    dataIndex: 'quantitativePackageSpecs',
    width: 150,
  },
  {
    title: '定数包数量',
    align: 'center',
    dataIndex: 'packageNum',
    width: 120,
  },
  {
    title: '规格',
    align: 'center',
    dataIndex: 'goodsSpecs',
    width: 120,
  },
  {
    title: '型号',
    align: 'center',
    dataIndex: 'goodsSpecsDetail',
    width: 120,
  },
  {
    title: '效期',
    align: 'center',
    dataIndex: 'term',
    sorter:true,
    width: 150,
  },
  {
    title: '当前库存',
    align: 'center',
    dataIndex: 'totalNum',
    width: 100,
  },
  {
    title: '可出库数量',
    align: 'center',
    dataIndex: 'currentOutStoreNum',
    slots: { customRender: 'currentOutStoreNum' },
    width: 100,
  },
  {
    title: '单位',
    align: 'center',
    dataIndex: 'unitName',
    width: 80,
  },
  {
    title: '批号',
    align: 'center',
    dataIndex: 'batchNo',
    width: 120,
  },
  {
    title: '确认日期',
    align: 'center',
    dataIndex: 'acceptDate',
    sorter:true,
    width: 150,
  },
  {
    title: '单价',
    align: 'center',
    dataIndex: 'goodsPrice',
    sorter:true,
    width: 120,
  },
  {
    title: '金额',
    align: 'center',
    dataIndex: 'billTotalAmt',
    slots: { customRender: 'billTotalAmt' },
    width: 120,
  },
  {
    title: '品牌',
    align: 'center',
    dataIndex: 'brand',
    width: 150,
  },
  {
    title: '生产厂商',
    align: 'center',
    dataIndex: 'manufacturerName',
    
  },
];

export const lowFormSchema: FormSchema[] = [
  {
    label: '物资名称',
    field: 'goodsName',
    component: 'Input',
  },
  {
    label: '物资编码',
    field: 'goodsCode',
    component: 'Input',
  },
  {
    label: '规格',
    field: 'goodsSpecs',
    component: 'Input',
  },
  {
    label: '型号',
    field: 'goodsSpecsDetail',
    component: 'Input',
  },
  {
    label: '生产厂商',
    field: 'manufacturerName',
    component: 'Input',
  },
  //{
  //  label: '是否定数包',
  //  field: 'quantitativePackageFlag',
  //  component: 'JDictSelectTag',
  //  componentProps: () => {
  //    return {
  //      dictCode: 'yn',
  //    };
  //  },
  //},
];
export const lowPacksgesFormSchema: FormSchema[] = [
  {
    label: '物资名称',
    field: 'goodsName',
    component: 'JInput',
  },
  {
    label: '物资编码',
    field: 'goodsCode',
    component: 'JInput',
  },
  {
    label: '规格',
    field: 'goodsSpecs',
    component: 'JInput',
  },
  {
    label: '型号',
    field: 'goodsSpecsDetail',
    component: 'JInput',
  },
  {
    label: '生产厂商',
    field: 'manufacturerName',
    component: 'JInput',
  },
  {
    label: '确认日期',
    field: 'acceptDate',
    component: 'RangePicker',
    componentProps: { valueType: 'Date'},
  },
];
//出库单详情列表
export const transferDetailColumns: BasicColumn[] = [
  {
    title: '耗材名称',
    align: 'center',
    dataIndex: 'goodsName',
  },
  {
    title: '耗材编码',
    align: 'center',
    dataIndex: 'goodsCode',
  },
  {
    title: '批次码',
    align: 'center',
    dataIndex: 'batchCode',
    width:220
  },
  {
    title: '明细数量',
    align: 'center',
    dataIndex: 'outStoreDetailNum',
  },
  {
    title: '单位',
    align: 'center',
    dataIndex: 'unitName',
  },
  {
    title: '价格(元)',
    align: 'center',
    dataIndex: 'goodsPrice',
  },
  {
    title: '规格',
    align: 'center',
    dataIndex: 'goodsSpecs',
  },
  {
    title: '具体规格',
    align: 'center',
    dataIndex: 'goodsSpecsDetail',
  },
  {
    title: '生产厂商',
    align: 'center',
    dataIndex: 'manufacturerName',
  },
  {
    title: '供应商名称',
    align: 'center',
    dataIndex: 'supplierName',
  },
  {
    title: '备注',
    align: 'center',
    dataIndex: 'remark',
  },
];
//列表数据
export const columnsRecord: BasicColumn[] = [
  // {
  //   title: '耗材编码',
  //   align: 'center',
  //   dataIndex: 'goodsCode',
  // },
  // {
  //   title: '耗材名称',
  //   align: 'center',
  //   dataIndex: 'goodsName',
  // },
  {
    title: '耗材条码',
    align: 'center',
    dataIndex: 'uniqueCode',
    width:220
  },
  {
    title: '灭菌编号',
    align: 'center',
    dataIndex: 'sterilizationBatch',
  },
  {
    title: '灭菌日期',
    align: 'center',
    dataIndex: 'sterilizationDate',
  },
  {
    title: '资金来源',
    align: 'center',
    dataIndex: 'fundSource_dictText',
  },
  {
    title: '结算类型',
    align: 'center',
    dataIndex: 'settlementType_dictText',
  },
  {
    title: '是否UDI',
    align: 'center',
    dataIndex: 'udiFlag_dictText',
  },
  {
    title: '出库单流水号',
    align: 'center',
    dataIndex: 'outStoreNo',
  },
  // {
  //   title: '计费码',
  //   align: 'center',
  //   dataIndex: 'goodsAccountCode',
  // },
  {
    title: 'udi码',
    align: 'center',
    dataIndex: 'udiDi',
  },
  // {
  //   title: '批次',
  //   align: 'center',
  //   dataIndex: 'batchNo',
  // },
  // {
  //   title: '规格',
  //   align: 'center',
  //   dataIndex: 'goodsSpecs',
  // },
  // {
  //   title: '具体规格',
  //   align: 'center',
  //   dataIndex: 'goodsSpecsDetail',
  // },
  // {
  //   title: '有效期',
  //   align: 'center',
  //   dataIndex: 'term',
  // },
  // {
  //   title: '单位',
  //   align: 'center',
  //   dataIndex: 'unitName',
  // },
  // {
  //   title: '所在科室或部门名称',
  //   align: 'center',
  //   dataIndex: 'departName',
  // },
  // {
  //   title: '生产日期',
  //   align: 'center',
  //   dataIndex: 'productDate',
  // },
  // {
  //   title: '生产厂家',
  //   align: 'center',
  //   dataIndex: 'manufacturerName',
  // },
  // {
  //   title: '供应商',
  //   align: 'center',
  //   dataIndex: 'supplierName',
  // },
  // {
  //   title: '备注',
  //   align: 'center',
  //   dataIndex: 'remark',
  // },
];