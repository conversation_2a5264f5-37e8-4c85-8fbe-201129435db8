<template>
  <a-input ref="input" :value="innerValue" v-bind="cellProps" @blur="handleBlur" @change="handleChange" @input="handleInput" @keydown="handleKeydown" />
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  import { isString } from '/@/utils/is';
  import { JVxeComponent, JVxeTypes } from '/@/components/jeecg/JVxeTable/types';
  import { useJVxeComponent, useJVxeCompProps } from '/@/components/jeecg/JVxeTable/hooks';

  export default defineComponent({
    name: 'JVxeInputCell',
    props: {
      ...useJVxeCompProps(),
      // 添加小数位数配置参数，默认为4位
      decimalLength: {
        type: Number,
        default: 4
      }
    },
    setup(props: JVxeComponent.Props & { decimalLength?: number }) {

      
      const { innerValue, cellProps, handleChangeCommon } = useJVxeComponent(props);
      /** 处理键盘按下事件，阻止非法字符输入 */
      function handleKeydown(event) {
        const key = event.key;
        const target = event.target;
        const currentValue = target.value;
        
        // 允许的按键: 控制键
        const controlKeys = [
          'Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 
          'Tab', 'Enter', 'Home', 'End'
        ];
        
        // 如果是允许的控制键，直接允许
        if (controlKeys.includes(key)) {
          return;
        }
        
        // 如果是 Ctrl 或 Cmd 组合键（如 Ctrl+C, Ctrl+V），允许
        if (event.ctrlKey || event.metaKey) {
          return;
        }
        
        // 检查是否是数字
        if (/^[0-9]$/.test(key)) {
          return;
        }
        
        // 特殊处理负号: 只能出现在开头
        if (key === '-' && target.selectionStart === 0) {
          // 检查当前值是否已经包含负号
          if (currentValue.startsWith('-')) {
            // 如果已经有负号，阻止再次输入
            event.preventDefault();
          }
          return;
        }
        
        // 特殊处理小数点: 最多只能出现一次
        if (key === '.') {
          // 检查当前值是否已经包含小数点
          if (currentValue.includes('.')) {
            // 如果已经有小数点，阻止再次输入
            event.preventDefault();
          }
          return;
        }
        
        // 阻止其他所有非法字符输入
        event.preventDefault();
      }

      /** 处理input事件，实时限制输入 */
      function handleInput(event) {
        // 获取输入的值
        let value = event.target.value;
        
        // 获取配置的小数位数，默认为4
        const decimalLength = props.decimalLength || 4;
        
        // 验证输入值是否符合浮点数格式
        const regex = new RegExp(`^-?\\d*\\.?\\d{0,${decimalLength}}$`);
        
        // 如果不符合规则，则恢复到之前的值
        if (!regex.test(value)) {
          // 从当前值中移除非数字、非小数点、非负号的字符
          let validValue = '';
          let decimalPointCount = 0;
          let negativeSignAdded = false;
          
          for (let i = 0; i < value.length; i++) {
            const char = value[i];
            
            // 处理负号：只能在开头
            if (char === '-' && i === 0) {
              if (!negativeSignAdded) {
                validValue += char;
                negativeSignAdded = true;
              }
            } 
            // 处理小数点：只能有一个
            else if (char === '.') {
              if (decimalPointCount < 1) {
                validValue += char;
                decimalPointCount++;
              }
            } 
            // 处理数字
            else if (/[0-9]/.test(char)) {
              // 检查小数点后的位数是否超过限制
              const dotIndex = validValue.indexOf('.');
              if (dotIndex === -1 || validValue.substring(dotIndex + 1).length < decimalLength || char === '.') {
                validValue += char;
              }
            }
          }
          
          // 更新值
          value = validValue;
          innerValue.value = value;
          event.target.value = value;
        } else {
          // 如果符合规则，则更新值
          innerValue.value = value;
        }
        
        // 触发通用变更处理
        handleChangeCommon(value);
      }

      /** 处理change事件 */
      function handleChange(event) {
        let { target } = event;
        let { value } = target;
        handleChangeCommon(value);
      }

      /** 处理blur失去焦点事件 */
      function handleBlur(event) {
        let { target } = event;
        handleChangeCommon(target.value);
      }

      return {
        innerValue,
        cellProps,
        handleInput,
        handleChange,
        handleBlur,
        handleKeydown
      };
    },
    enhanced: {
      installOptions: {
        autofocus: '.ant-input',
      },
      getValue(value, ctx) {
        // 从ctx中获取配置的小数位数，默认为4
        const params = ctx?.props?.params || {};
        const decimalLength = params.decimalLength || ctx?.props?.decimalLength || 4;
        
        const NumberRegExp = new RegExp(`^-?(\\d+\\.?\\d{0,${decimalLength}}|\\d*\\.\\d{1,${decimalLength}}|\\d+)$`);
        const InputProcessRegExp = /^-?(\d+\.?)$/;
        
        if ((ctx?.props?.type === JVxeTypes.inputNumber || ctx?.props?.type === JVxeTypes.floatInput) && isString(value)) {
          if (NumberRegExp.test(value) && value !== '.') {
            return Number.parseFloat(value);
          } else if (InputProcessRegExp.test(value)) {
            // 对于输入过程中的格式，如 "123."，直接返回原值
            return value;
          }
        }
        return value;
      },
    } as JVxeComponent.EnhancedPartial,
  });
</script>