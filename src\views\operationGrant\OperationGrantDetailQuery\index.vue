<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <template #footer="currentPageData">
        <div class="foot pd6 dpflex jcsb">
          <div>合计</div>
          <div class="foot-total">{{ getFootTotal(currentPageData) }}</div>
        </div>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" name="operationGrant-OperationGrantDetailQuery" setup>
import { onActivated, ref, nextTick } from 'vue';
import { BasicTable, FormSchema } from "/@/components/Table";
import { useListPage } from "/@/hooks/system/useListPage";
import { columns } from "./index.data";
import { list, getList } from "./index.api";
import { handleSummaryNew } from "/@/utils/index";
import { getspdStoragelist, getDepartListLeaf, queryMyDeptListByTenantId } from '/@/api/common/api';
import { useRoute } from 'vue-router';
import moment from 'moment';
import dayjs from 'dayjs';
const route = useRoute();
import { useUserStore } from "/@/store/modules/user";
const userStore: any = useUserStore();
const departId = ref(userStore.hospitalZoneInfo.depart.id)
const departAllFlag = ref(1)
const checkRouteParams = async (newRoute) => {
  const res = await getList({ pageNo: 1, pageSize: 10,departId:departId.value })
  departAllFlag.value = res?.departAllFlag // 1 管理员  0 普通角色
  //首页跳过去  
  if (newRoute.params?.isJump === "1") {
    getForm().setFieldsValue({ operationSuppliersStatus_MultiString: ['0', '5'], requestDepartIds: [newRoute.params.departId] }); // 已发放 已退费
    getForm().updateSchema({
      field: 'requestDepartIds',
      required: true,
      componentProps: {
        api: queryMyDeptListByTenantId,
        labelField: 'departName',
        optionFilterProp: 'label',
        valueField: 'id',
        allowClear: false,
      }
    })
  } else {
    // 其他页面跳转过去需要权限判断
    getForm().setFieldsValue({ operationSuppliersStatus_MultiString: [], requestDepartIds: departAllFlag.value === 1 ? [] : [departId.value]});
    getForm().updateSchema({
      field: 'requestDepartIds',
      required: departAllFlag.value === 1 ? false : true,
      componentProps: {
        api: departAllFlag.value === 1 ? getDepartListLeaf : queryMyDeptListByTenantId,
        allowClear: true,
      }
    })
  }
  reload()
}
onActivated(() => {
  checkRouteParams(route);
})

const ranges = {
  今天: [moment().startOf("day"), moment()],
  明天: [
    moment().startOf("day"),
    moment().startOf("day").subtract(-1, "days"),
  ],
}

const searchFormSchema: FormSchema[] = [
  {
    label: '发放日期',
    field: 'sendDate',
    component: 'RangePicker',
    componentProps: { valueType: 'Date', ranges: ranges },
    //默认时间范围是前一个月
    defaultValue: [dayjs().subtract(1, 'month'), dayjs()],
  },
  {
    label: '耗材条码',
    field: 'inventoryDetailRecord.uniqueCode',
    component: 'JInput',
  },
  {
    label: '手术科室',
    field: 'departIds',
    component: 'ApiSelect',
    componentProps: {
      api: getDepartListLeaf,
      showSearch: true,
      optionFilterProp: 'departNameDepartNameAbbr',
      labelField: 'departName',
      valueField: 'id',
      allowClear: true,
    },
  },
  {
    label: '申领科室',
    field: 'requestDepartIds',
    component: 'ApiSelect',
    componentProps: {
      api: getDepartListLeaf,
      showSearch: true,
      optionFilterProp: 'departNameDepartNameAbbr',
      labelField: 'departName',
      valueField: 'id',
      mode: 'multiple',
    },
  },
  {
    label: '物资名称',
    field: 'inventoryDetailRecord.goodsName',
    component: 'JInput',
  },
  {
    label: '物资编码',
    field: 'inventoryDetailRecord.goodsCode',
    component: 'JInput',
  },
  {
    label: '规格',
    field: 'inventoryDetailRecord.goodsSpecs',
    component: 'JInput',
  },
  {
    label: '型号',
    field: 'inventoryDetailRecord.goodsSpecsDetail',
    component: 'JInput',
  },
  {
    label: '病人姓名',
    field: 'operationOrder.patientName',
    component: 'JInput',
  },
  {
    label: '住院号',
    field: 'operationOrder.liveHospitalCode',
    component: 'JInput',
  },
  {
    label: "业务类型",
    field: "operationSuppliersStatus_MultiString",
    component: "JDictSelectTag",
    componentProps: {
      dictCode: "operation_suppliers_status",
      mode: 'multiple',
    },
  },
  {
    label: 'UDI',
    field: 'udiCode',
    component: 'JInput',
  },
  {
    label: '出库仓库',
    field: 'operationOrder.storageId',
    component: 'ApiSelect',
    componentProps: {
      api: () => getspdStoragelist({ column: 'storageType', order: 'asc' }),
      params: {},
      showSearch: true,
      optionFilterProp: 'storageNameAbbr',
      labelField: 'storageName',
      valueField: 'id',
    },
  },
  {
    label: '入库仓库',
    field: 'operationOrder.requestStorageId',
    component: 'ApiSelect',
    componentProps: {
      api: () => getspdStoragelist({ column: 'storageType', order: 'asc' }),
      params: {},
      showSearch: true,
      optionFilterProp: 'storageNameAbbr',
      labelField: 'storageName',
      valueField: 'id',
    },
  },
  {
    label: "核对状态",
    field: "contrastPriceStatus",
    component: "JDictSelectTag",
    componentProps: {
      dictCode: "charge_check_status",
    },
  },
];

const getFootTotal = (currentPageData) =>
  handleSummaryNew(currentPageData, "goodsPrice", "sendNum");
//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    api: list,
    columns,
    canResize: true,
    ellipsis: true,
    showIndexColumn: true,
    immediate: false,
    scroll: { y: 480 },
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToTime: [
        ["sendDate", ["sendDate_begin", "sendDate_end"], "YYYY-MM-DD"],
      ],
    },
    indexColumnProps: {
      fixed: "left",
    },
    showActionColumn: false,
    beforeFetch(params) {
      if (params.column == 'createTime') {
        params.column = "sendDate";
      }
      params.operationOrder = null;
      params.operationScheduling = null;
      params.inventoryDetailRecord = null;
    },
  },
});

const [registerTable, { reload, getForm }, { selectedRowKeys }] = tableContext;

</script>
<style scoped></style>
