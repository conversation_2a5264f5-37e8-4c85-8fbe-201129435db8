<template>
  <div class="big-data-demo-container">
    <a-page-header title="大数据表格演示" sub-title="演示JVxeTable处理大量数据的性能" style="background: #fff; margin-bottom: 20px">
      <template #extra>
        <a-button key="2" @click="handleShowData">查看数据</a-button>
        <a-button key="1" type="primary" @click="handleImportData">添加数据</a-button>
      </template>
    </a-page-header>

    <!-- 添加导入数据的模态框 -->
    <a-modal v-model:visible="importModalVisible" title="导入数据" @ok="confirmImportData" @cancel="cancelImportData" class="import-data-modal" :width="800">
      <a-form :model="importForm" layout="vertical">
        <a-form-item label="数据格式说明">
          <a-alert type="info" class="data-format-alert">
            <template #message>
              请按照以下格式输入数据（每行一条记录，字段用逗号分隔）：
              <br />物资编码,物资名称,货位,规格 <br />例如： <br />GD001,医用口罩,仓库A,一次性 <br />GD002,消毒液,仓库B,500ml
            </template>
          </a-alert>
        </a-form-item>
        <a-form-item label="导入数据">
          <a-textarea v-model:value="importForm.dataText" :rows="6" placeholder="请输入要导入的数据，每行一条记录..." class="data-import-textarea" />
        </a-form-item>
      </a-form>
    </a-modal>

    <a-card title="大数据表格展示" :bordered="true">
      <BigDataTable
        ref="bigDataTableRef"
        :searchable="configForm.searchable"
        :maxHeight="configForm.maxHeight"
        :popupQueryUrl="configForm.popupQueryUrl"
        :queryParam="configForm.queryParam"
        :columns="configForm.columns"
        :popupColumns="configForm.popupColumns"
        :tableHeight="configForm.tableHeight"
        :numberField="configForm.numberField"
      >
        <!-- 使用插槽添加自定义按钮 -->
        <template #tableButtons>
          <a-button type="primary" @click="handleImportData">添加数据</a-button>
        </template>
      </BigDataTable>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue';
import BigDataTable from '../jeecg/JVxeTableDemo/big-data-demo/BigDataTable.vue';
import { JVxeTypes, JVxeTableInstance } from '/@/components/jeecg/JVxeTable/types';
import { useMessage } from '/@/hooks/web/useMessage';

const { createMessage } = useMessage();
// 在父组件中
const bigDataTableRef = ref(null);
// 配置表单数据
const configForm = reactive({
  searchable: true,
  maxHeight: 480,
  tableHeight:500,
  popupQueryUrl: '/spd/spdGoodsCommon/goodsBizVoList',
  queryParam: 'goodsName__goodsSimpleCode__goodsCommonName__goodsCode__goodsSpecs_orFlag',
  columns: [
    {
      title: '物资编码',
      key: 'goodsCode',
      type: JVxeTypes.normal,
      width: 180,
      align: 'center',
      fixed: 'left',
      sortable: true,
    },
    {
      title: '物资名称',
      key: 'goodsName',
      type: JVxeTypes.input,
      width: 280,
      align: 'center',
      sortable: true,
    },
    {
      title: '货位',
      key: 'locationName',
      type: JVxeTypes.normal,
      width: 120,
      align: 'center',
      sortable: true,
    },
    {
      title: '规格',
      key: 'goodsSpecs',
    },
    {
        title: '数量',
        key: 'number_float',
        type: JVxeTypes.floatInput,
        width: 180,
        statistics: ['sum'],
        align: 'center',
        sortable: true,
      },
    //操作按钮插槽必须配置
    {
      title: '操作',
      key: 'action',
      type: JVxeTypes.slot,
      fixed: 'right',
      minWidth: 100,
      align: 'center',
      slotName: 'myAction',
    },
  ],
  popupColumns: [
    {
      title: '物资编码',
      key: 'goodsCode',
      width: 180,
    },
    {
      title: '物资名称',
      key: 'goodsName',
      width: 150,
    },
    {
      title: '别名',
      key: 'goodsCommonName',
      width: 120,
    },
    //操作按钮插槽必须配置
    {
      title: '操作',
      key: 'action',
      type: JVxeTypes.slot,
      fixed: 'right',
      minWidth: 100,
      align: 'center',
      slotName: 'myAction',
    },
  ],
  numberField: 'number_float',
  
});
// 导入数据模态框可见性
const importModalVisible = ref(false);
// 导入表单数据
const importForm = reactive({
  dataText: '',
});
// 处理导入数据按钮点击事件
const handleImportData = () => {
  importModalVisible.value = true;
  importForm.dataText = '';
};
// 确认导入数据
const confirmImportData = () => {
  if (!importForm.dataText.trim()) {
    createMessage.warning('请输入要导入的数据');
    return;
  }

  try {
    // 解析输入的数据
    const lines = importForm.dataText.trim().split('\n');
    const importData: any = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line) {
        const parts = line.split(',');
        if (parts.length >= 4) {
          importData.push({
            goodsCode: parts[0],
            goodsName: parts[1],
            locationName: parts[2],
            goodsSpecs: parts[3],
            id: `import_${Date.now()}_${i}`, // 生成唯一ID
            rowIndex: (importData.length + 1).toString(),
          });
        } else {
          createMessage.warning(`第${i + 1}行数据格式不正确，已跳过`);
        }
      }
    }

    // 调用子组件的导入方法
    if (bigDataTableRef.value && importData.length > 0) {
      const result = bigDataTableRef.value.importDataSource(importData);
      if (result.success) {
        createMessage.success(result.message);
        importModalVisible.value = false;
      } else {
        createMessage.error(result.message);
      }
    } else if (importData.length === 0) {
      createMessage.warning('没有有效的数据可以导入');
    }
  } catch (error) {
    createMessage.error('导入数据时发生错误：' + error.message);
  }
};

// 取消导入数据
const cancelImportData = () => {
  importModalVisible.value = false;
  importForm.dataText = '';
};
const handleShowData = () => {
  if (bigDataTableRef.value) {
    const data = bigDataTableRef.value.getCurrentDataSource();
    console.log('当前数据:', data);
  }
};

const handleAddData = () => {
  console.log('添加数据');
};
</script>

<script lang="ts">
export default {
  name: 'BigDataDemo',
};
</script>

<style scoped>
.big-data-demo-container {
  padding: 20px;
  background-color: #f0f2f5;
  height: 100%;
}

:deep(.ant-card) {
  border-radius: 18x;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.ant-form {
  padding: 20px !important;
}
/* 导入数据模态框样式 */
:deep(.import-data-modal .ant-modal-content) {
  border-radius: 12px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

:deep(.import-data-modal .ant-modal-header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  padding: 20px 24px;
}

:deep(.import-data-modal .ant-modal-title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

:deep(.import-data-modal .ant-modal-close) {
  color: white;
}

:deep(.import-data-modal .ant-modal-close:hover) {
  color: #f0f0f0;
}

:deep(.import-data-modal .ant-modal-body) {
  padding: 24px;
}

:deep(.import-data-modal .ant-form-item) {
  margin-bottom: 20px;
}

:deep(.import-data-modal .ant-form-item-label > label) {
  font-weight: 500;
  color: #333;
  padding-bottom: 8px;
}

/* 数据格式说明样式 */
:deep(.data-format-alert) {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 8px;
  padding: 16px;
}

:deep(.data-format-alert .ant-alert-message) {
  color: #52c41a;
  font-size: 13px;
  line-height: 1.6;
}

/* 文本域样式 */
:deep(.data-import-textarea) {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  background-color: #fafafa;
  transition: all 0.3s;
  padding: 12px;
}

:deep(.data-import-textarea:hover) {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

:deep(.data-import-textarea:focus) {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  background-color: #fff;
}

:deep(.import-data-modal .ant-modal-footer) {
  padding: 16px 24px 24px;
  border-top: none;
  text-align: right;
  background: #fafafa;
  border-radius: 0 0 12px 12px;
}

:deep(.import-data-modal .ant-btn) {
  border-radius: 6px;
  padding: 0 24px;
  height: 36px;
  font-size: 14px;
}

:deep(.import-data-modal .ant-btn-primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.4);
}

:deep(.import-data-modal .ant-btn-default) {
  border-color: #d9d9d9;
  color: #666;
}
</style>