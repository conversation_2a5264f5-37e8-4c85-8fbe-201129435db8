<template>
  <div>
    <BasicModal v-bind="$attrs" @register="registerModal" title="创建退货单" width="1500px" @ok="handleSubmit"
      :dragHandle="null" :maskClosable="false" defaultFullscreen>
      <BasicTable bordered rowKey="id" :action-column="actionColumn" :columns="planColumns" :dataSource="innerData"
        :scroll="{ x: 1000, y: 1000 }" @edit-change="onEditChange" class="ant-table-striped" size="small"
        :rowClassName="rowClassName">
        <template #tableTitle>
          <span class="api-select-addon"> <span style="color: red; font-size: 16px">*</span>退货仓库:</span>
          <ApiSelect :api="() =>
              getspdStoragelist({
                column: 'storageType,simpleCode',
                order: 'asc,asc',
                storageType_MultiString: '1',
                delFlag: 0,
              })
            " v-model:value="sourceStorageId" showSearch placeholder="请选择退货仓库" optionFilterProp="storageNameAbbr"
            labelField="storageName" valueField="id" style="width: 200px">
          </ApiSelect>
          <a-input v-model:value="code" type="like" style="width: 330px" placeholder="请扫描物资条码或批次码"
            @keyup.enter="search">
            <template #addonBefore>
              <span>物资条码/批次码：</span>
            </template>
          </a-input>
          <span class="api-select-addon">采购员:</span>
          <a-select allowClear v-model:value="purchaser" show-search placeholder="请输入采购员" style="width: 200px"
            :default-active-first-option="false" :show-arrow="false" :filter-option="false" :not-found-content="null"
            :options="userList" @search="handleSearch" @change="handleChange">
          </a-select>
          <span class="api-select-addon">退货说明:</span>
          <a-input v-model:value="remark" placeholder="退货说明" style="width: 350px" show-count :maxlength="60" />
          <a-button type="primary" @click="openModals">选择物资</a-button>
          <a-button type="primary" @click="openReturnModals">选择退库单</a-button>
          <div class="box-title">
            <div><span class="ant-pro-table-title" style="background: rgb(250, 223, 223)"></span>退货数量大于库存</div>
            <div><span class="ant-pro-table-title" style="background: grey"></span>已退货物资</div>
          </div>
        </template>
        <template #returnPurchaseNum="{ record }">
          <a-input @input="changeInp(record)" v-model:value="record.returnPurchaseNum"
            :disabled="record.quantitativePackageFlag == 1 || record.individualFlag == 1"></a-input>
        </template>
        <template #action="{ record }">
          <TableAction :actions="[
            {
              label: '删除',
              icon: 'ic:outline-delete-outline',
              onClick: handleDel.bind(null, record),
            },
          ]" />
        </template>
      </BasicTable>
    </BasicModal>

    <MaterialSelectModal @register="regModal" @getSelectRow="setValue"></MaterialSelectModal>
    <returnOrderModal @register="returnModal" @getSelectRow="setValue"></returnOrderModal>
  </div>
</template>
<script lang="ts" setup>
import { BasicModal, useModalInner } from '/@/components/Modal';
import { useModal } from '/@/components/Modal';
import { ApiSelect } from '/@/components/Form/index';
import MaterialSelectModal from './MaterialSelectModal.vue';
import returnOrderModal from './returnOrderModal.vue';
import { ref, reactive, computed } from 'vue';
import { BasicTable, TableAction } from '/@/components/Table';
import { planColumns } from '../SpdDirectTransfer.data';
import { outStore, getUserList, getspdStoragelist, inventoryDetailPageList } from '../SpdDirectTransfer.api';
import { message } from 'ant-design-vue';
import { useMessage } from '/@/hooks/web/useMessage';
const { createConfirm } = useMessage();

const remark = ref('');
let code: any = ref(null);
let timeout: any;
function fetch(value: string, callback: any) {
  if (timeout) {
    clearTimeout(timeout);
    timeout = null;
  }
  function fake() {
    getUserList({ keyWord: value }).then((res) => {
      res.forEach((item) => {
        item.value = item.username;
        item.label = item.realname;
      });
      callback(res);
    });
  }
  timeout = setTimeout(fake, 300);
}
const handleSearch = (val: string) => {
  fetch(val, (d: any[]) => (userList.value = d));
};
const handleChange = (val: string) => {
  subForm.applyUser = val;
  fetch(val, (d: any[]) => (userList.value = d));
};
const userList = ref<any>([]);
const sourceStorageId = ref();
const purchaser = ref();
const innerData = ref<any[]>([]);
const showFooterFlg = ref(true);
const isUpdate = ref(true);
const actionColumn: any = {
  width: 100,
  title: '操作',
  dataIndex: 'action',
  slots: { customRender: 'action' },
  fixed: 'right',
};
const emit = defineEmits(['success']);
//注册model
const [regModal, { openModal }] = useModal();
const [returnModal, { openModal: openReturnModal }] = useModal();
const openModals = (record) => {
  if (sourceStorageId.value) {
    openModal(true, {
      record,
      sourceStorageId: sourceStorageId.value,
      isUpdate: false,
      showFooter: false,
    });
  } else {
    message.warning('请选择退货仓库');
  }
};
const openReturnModals = (record) => {
  if (sourceStorageId.value) {
    openReturnModal(true, {
      record,
      sourceStorageId: sourceStorageId.value,
      isUpdate: false,
      showFooter: false,
    });
  } else {
    message.warning('请选择退货仓库');
  }
};
//表单赋值
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  innerData.value = [];
  subForm.detailList = [];
  //arr.value = [];
  remark.value = '';
  subForm.applyUser = undefined;
  sourceStorageId.value = undefined;
  setModalProps({
    confirmLoading: false,
    showCancelBtn: !!data?.showFooter,
    showOkBtn: !!data?.showFooter,
  });
  isUpdate.value = !!data?.isUpdate;
  showFooterFlg.value = data?.showFooter;
});

const changeInp = (record) => {
  record.returnPurchaseNum = record.returnPurchaseNum.replace(/[^0-9.]/g, '');
  const num = record.returnPurchaseNum.split('.');
  if (num.length > 1) {
    num[1] = num[1].slice(0, 4);
    record.returnPurchaseNum = num.join('.');
    record.returnPurchaseNum = record.returnPurchaseNum.replace(/(\.\d*?[1-9])0+$/, '$1');
  }
  if (+record.returnPurchaseNum > +record.currentNum) {
    record.returnPurchaseNum =record.currentNum;
    return message.warning('出库数量不能大于当前库存数量');
  }
};
/**
 * 返回勾选数据
 */
function setValue(options) {
  options.forEach((item) => {
    let arr = innerData.value.filter((v) => v.id == item.id);
    if (arr.length) {
      if (arr[0].individualFlag != 1 && arr[0].quantitativePackageFlag != 1) {
        if (+arr[0]['returnPurchaseNum'] + +item.returnPurchaseNum <= +item.currentNum) {
          arr[0]['returnPurchaseNum'] = +arr[0]['returnPurchaseNum'] + +item.returnPurchaseNum;
        } else {
          arr[0]['returnPurchaseNum'] = +item.currentNum;
        }
      }
    } else {
      innerData.value.push(item);
    }
  });
  //innerData.value排序returnPurchaseFlag==1的排到最后
  innerData.value.sort(function (a, b) {
    if (a.returnPurchaseFlag == 1) {
      return 1;
    } else {
      return -1;
    }
  });
}
const subForm = reactive<any>({
  applyOrderId: 0,
  applyUser: '',
  detailList: [],
});

/* 
扫码添加数据事件
*/
async function search() {
  if (sourceStorageId.value == null) {
    message.warning('请选择科室仓库');
  } else {
    if (code.value != null) {
      let parmes = {
        code: code.value,
        operationFlag: 1,
        storageId: sourceStorageId.value,
      };

      let res = await inventoryDetailPageList(parmes);
      if (res.length == 0) {
        message.warning('条码不正确，请重新扫码');
      } else {
        let arr = innerData.value.filter((v) => v.id == res[0].id);
        if (arr.length) {
          message.warning('物资已添加');
        } else {
          if (res[0].quantitativePackageFlag == 0) {
            res[0].returnPurchaseNum = 1;
            innerData.value.push(res[0]);
          } else if (res[0].quantitativePackageFlag == 1) {
            res[0].returnPurchaseNum = res[0].currentOutStoreNum;
            innerData.value.push(res[0]);
          }
        }
      }
    } else {
      message.warning('请输入或使用扫码枪扫码');
    }
    setTimeout(() => {
      code.value = null;
    }, 200);
  }
}

//表单提交事件
async function handleSubmit() {
  let arr = ref<any>([]);
  if (purchaser.value && innerData.value.length > 0) {
    setModalProps({ confirmLoading: true });
    try {
      innerData.value.forEach((item) => {
        if (item.returnPurchaseFlag == 0) {
          if (item.inventoryDetailId != null) {
            const existingItem = arr.value.find((detail) => detail.inventoryDetailId === item.inventoryDetailId);
            if (existingItem) {
              existingItem.inventoryRecordIds.push(item.id);
            } else {
              arr.value.push({
                goodsCode: item.goodsCode,
                inventoryDetailId: item.inventoryDetailId,
                inventoryRecordIds: [item.id],
                supplierId: item.supplierId,
                settlementType: item.settlementType,
                storageId: item.storageId,
                currentNum: item.currentNum,
              });
            }
          } else {
            arr.value.push({
              goodsCode: item.goodsCode,
              inventoryDetailId: item.id,
              inventoryDetailNum: item.returnPurchaseNum,
              supplierId: item.supplierId,
              settlementType: item.settlementType,
              storageId: item.storageId,
              currentNum: item.currentNum,
            });
          }
        }
      });

      arr.value.forEach((item) => {
        if (item.inventoryRecordIds != null) {
          item.inventoryRecordIds = [...new Set(item.inventoryRecordIds)];
          item.inventoryDetailNum = item.inventoryRecordIds.length;
        }
      });
      let obj = {
        applyUser: subForm.applyUser,
        detailList: arr.value,
        pushFlag: 0,
        purchaseUser: purchaser.value,
        remark: remark.value,
        storageId: sourceStorageId.value, //源仓库
      };

      //关闭弹窗
      const res = await outStore(obj);

      arr.value = [];
      purchaser.value = null;
      closeModal();
      //刷新列表
      emit('success');
    } catch (e) {
      setModalProps({ confirmLoading: false });
    } finally {
      setModalProps({ confirmLoading: false });
    }
  } else if (innerData.value.length <= 0) {
    message.error('请选择物资');
  } else if (!purchaser.value) {
    message.error('请选择采购员');
  }
}
function handleDel(record: Recordable) {
  const index = innerData.value.findIndex((item) => item.id === record.id);
  innerData.value.splice(index, 1);
}
// 编辑事件
function onEditChange({ column, value, record }) {
  if (column.dataIndex === 'returnPurchaseNum') {
    if (record.quantitativePackageFlag == 0) {
      // 本例
      const temp = innerData.value.filter((item) => item.id === record.id);
      if (temp.length >= 1 && value >= 0) {
        if (value <= 0) {
          value = 1;
          message.warning('出库数量不能小于1，已更改为数量1');
        } else if (value > record.currentNum) {
          value = record.currentNum;
          message.warning('出库数量不能大于库存，已更改为最大数量');
        }
        temp[0].returnPurchaseNum = value;
      } else {
        message.warning('定数包管理物资数量不可修改');
        value = record.quantitativePackageNum;
      }
    }
  }
}
const rowClassName = computed(() => {
  return (record, index: number) => {
    if (record.returnPurchaseFlag == 1) {
      return 'grey';
    } else {
      if (record.returnPurchaseNum - 0 > record.currentNum - 0) {
        return 'darkRed';
      }
    }
  };
});
</script>
<style scoped lang="scss">
.api-select-addon {
  margin: 0 0 4px 0;
  position: relative;
  padding: 2px 11px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: normal;
  font-size: 14px;
  text-align: center;
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 2px 0 0 2px;
  transition: all 0.3s;
}

.ant-table-striped:deep(.darkRed) td {
  background-color: rgb(250, 223, 223) !important;
}

.ant-table-striped:deep(.grey) td {
  background-color: grey !important;
}

.ant-table-striped:deep(.box-title) {
  display: flex;
  justify-content: flex-end;
  /* 子元素靠右对齐 */
  align-items: center;
  /* 垂直居中（可选） */
}

.ant-table-striped:deep(.box-title) div {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ant-table-striped:deep(.box-title) .ant-pro-table-title {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 5px;
  margin-left: 10px;
  margin-right: 10px;
}
</style>