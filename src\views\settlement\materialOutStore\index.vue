<template>
  <div>
    <BasicTable
      bordered
      rowKey="id"
      @register="registerTable"
      :rowSelection="rowSelection"
    >
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'user:addh'" @click="addHighStore"
          >新增已计费材料出库</a-button
        >
        <a-button type="primary" v-auth="'user:addl'" @click="addLowStore"
          >新增材料出库</a-button
        >
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"> </TableAction>
      </template>
      <template #footer="currentPageData">
        <div class="foot pd6 dpflex jcsb">
          <div>合计</div>
          <div class="dpflex">
            <div class="ml100">{{ `${checkSumAmount}` }}</div>
            <div class="foot-total">{{ getFootTotal(currentPageData) }}</div>
        </div>
      </div>
    </template>
    </BasicTable>
    <DetailModal @register="DetailModals"></DetailModal>
    <HighsModal @register="HighsModals" @success="success"></HighsModal>
    <LowsModal @register="LowsModals" @success="success"></LowsModal>
    <!-- <SettleOrderModal @register="SettlesModal" @success="success"></SettleOrderModal> -->
  </div>
</template>

<script setup lang="ts" name="settlement-materialOutStore">
import { computed, ref } from "vue";
import { BasicTable, TableAction } from "/@/components/Table";
import { useListPage } from "/@/hooks/system/useListPage";
import { useModal } from "/@/components/Modal";
import DetailModal from "./components/DetailModal.vue";
import HighsModal from "./components/HighsModal.vue";
import LowsModal from "./components/LowsModal.vue";
import { list, pushBatch } from "./saleOutStore.api";
import { searchFormSchema, columns } from "./saleOutStore.data";
import { handleSummaryNew, checkedSum } from '/@/utils/index'
const getFootTotal = (currentPageData)=> handleSummaryNew(currentPageData,'totalAmt','consignmentNum')
const checkSumAmount = computed(() => checkedSum(checkedRows.value,'totalAmt','consignmentNum')) // 勾选合计
const [DetailModals, { openModal: Modal }] = useModal(); // 详情modal
const [HighsModals, { openModal: HighModal }] = useModal();
const [LowsModals, { openModal: LowModal }] = useModal();

let param: any = {};
/**
 * 选择列配置
 */
const checkedKeys = ref<Array<string | number>>([]);
const checkedRows = ref<any>([]);
const onSelectChange = (selectedRowKeys: (string | number)[], selectionRows) => {
  checkedKeys.value = selectedRowKeys;
  checkedRows.value = selectionRows;
};
const rowSelection = {
  type: "checkbox",
  columnWidth: 50,
  selectedRowKeys: checkedKeys,
  onChange: onSelectChange,
};
const addHighStore = () => {
  HighModal(true, {});
};
const addLowStore = () => {
  LowModal(true, {});
};

// }
const searchInfo = { consignmentType: 2 };
//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    title: "配送单",
    api: list,
    searchInfo: searchInfo,
    columns,
    canResize: false,
    scroll:{y:480},
    showIndexColumn: true,
    formConfig: {
      // labelWidth: 120,
      schemas: searchFormSchema,
      labelCol: { xxl:8 },
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToNumber: [],
      fieldMapToTime: [
        ["createTime", ["createTime_begin", "createTime_end"], "YYYY-MM-DD"],
      ],
    },
    actionColumn: {
      width: 200,
      fixed: "right",
    },
    beforeFetch(params) {
      param = JSON.parse(JSON.stringify(params));
    },
  },
});
const [registerTable, { reload, clearSelectedRowKeys, getForm }] = tableContext;
const success = () => {
  reload();
  clearSelectedRowKeys();
  console.log("成功");
};
/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: "查看",
      onClick: lookDetail.bind(null, record),
    },
    {
      label: "推送",
      popConfirm: {
        title: "是否确认推送",
        confirm: handlePush.bind(null, record),
      },
      ifShow: () => {
        return record.pushStatus != "1"; 
      },
    },
  ];
}
const lookDetail = (record: Recordable) => {
  Modal(true, {
    record,
    isUpdate: false,
    showFooter: false,
  });
};
const handlePush = async (record: Recordable) => {
  await pushBatch({ids:[record.id]});
  reload()
};
</script>

<style lang="scss" scoped>
</style>
