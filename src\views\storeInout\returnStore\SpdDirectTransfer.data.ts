// import { queryStoreList } from '/@/views/settlement/settleReview/settleManage.api'
import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { getspdStoragelist } from '/@/api/common/api';
import { ranges } from "/@/api/common/data";
//创建物资的列表数据
export const columns = [
  {
    title: '退库单号',
    dataIndex: 'returnOrderNo',
    width: 200,
    resizable: true,
  },
  {
    title: '退库仓库',
    dataIndex: 'sourceStorageName',
    width: 200,
    resizable: true,
  },
  {
    title: '入库仓库',
    dataIndex: 'targetStorageName',
    width: 200,
    resizable: true,
  },
  {
    title: '推送状态',
    dataIndex: 'pushFlag_dictText',
    width: 100,
    resizable: true,
    sorter: true,
  },
  {
    title: '发起人',
    dataIndex: 'requestUsername',
    width: 100,
    resizable: true,
  },
  {
    title: '审核状态',
    dataIndex: 'auditStatus_dictText',
    width: 100,
    resizable: true,
    sorter: true,
  },
  {
    title: '发起日期',
    dataIndex: 'requestDate',
    width: 100,
    resizable: true,
    sorter: true,
  },
  {
    title: '接收日期',
    dataIndex: 'acceptDate',
    width: 100,
    resizable: true,
    sorter: true,
  },
  {
    title: '退库原因',
    dataIndex: 'returnOrderCause',
    width: 150,
    resizable: true,
  },

];
// 
export const searchForm: FormSchema[] = [
  {
    label: '退库单号',
    field: 'returnOrderNo',
    component: 'JInput',
  },

  {
    label: '退库仓库',
    field: 'sourceStorageId',
    component: 'ApiSelect',
    componentProps: {
      type: 'like',
      value: [],
      api: getspdStoragelist,
      showSearch: true,
      // optionFilterProp: 'label',
      labelField: 'storageName', // label值
      optionFilterProp: 'storageNameAbbr',
      // resultField:'records', //  接口返回的字段，如果接口返回数组，可以不填。支持x.x.x格式
      valueField: 'id', // value值
      // mode: "multiple", // 支持多选
      allowClear: true
    },
  },
  {
    label: '入库仓库',
    field: 'targetStorageId',
    component: 'ApiSelect',
    componentProps: {
      type: 'like',
      value: [],
      api: getspdStoragelist,
      showSearch: true,
      // optionFilterProp: 'label',
      labelField: 'storageName', // label值
      optionFilterProp: 'storageNameAbbr',
      // resultField:'records', //  接口返回的字段，如果接口返回数组，可以不填。支持x.x.x格式
      valueField: 'id', // value值
      // mode: "multiple", // 支持多选
      allowClear: true
    },
  },
  {
    field: 'requestUsername',
    component: 'JInput',
    label: '发起人',
    colProps: {
      span: 8,
    },
  },
  {
    field: 'pushFlag',
    label: '推送状态',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'is_aggregator',
      };
    },
  },
  {
    field: 'auditStatus',
    label: '审核状态',
    component: 'JDictSelectTag',
    componentProps: () => {
      return {
        dictCode: 'audit_status',
      };
    },
  },
  {
    field: 'requestDate',
    component: 'RangePicker',
    label: '发起日期',
    componentProps: { valueType: 'Date', ranges: ranges }

  },
  {
    field: 'acceptDate',
    component: 'RangePicker',
    label: '接收日期',
    componentProps: { valueType: 'Date', ranges: ranges }
  },

]
//创建计划的列表数据
export const planColumns: BasicColumn[] = [
  {
    title: '物资编码',
    dataIndex: 'goodsCode',
    width: 180,
    fixed: 'left',
  },
  {
    title: '物资名称',
    dataIndex: 'goodsName',
    width: 150,
    fixed: 'left',
  },
  {
    title: '规格',
    dataIndex: 'goodsSpecs',
    width: 100,
  },

  {
    title: '型号',
    dataIndex: 'goodsSpecsDetail',
    width: 100,
  },
  {
    title: "批次码/定数包码",
    dataIndex: "batchCode",
    key: "batchCode",
    width: 200,
    align: 'center'
  },
  {
    title: "唯一码",
    dataIndex: "uniqueCode",
    key: "uniqueCode",
    width: 240,
    align: 'center'
  },
    {
    title: '响应库房',
    dataIndex: 'responseStorageName',
    width: 150,
  },
  {
    title: "是否定数包",
    dataIndex: "quantitativePackageFlag_dictText",
    width: 160,
    align: 'center'
  },
  {
    title: "定数包规格",
    dataIndex: "quantitativePackageSpecs",
    key: "quantitativePackageSpecs",
    width: 160,
    // ellipsis: true, // 设置ellipsis属性为true
    align: 'center'
  },
  {
    title: '科室库房',
    dataIndex: 'storageName',
    width: 150,
  },
  {
    title: '当前库存可用数量',
    dataIndex: 'currentNum',
    width: 160,
  },
//  {
//    title: '数量',
//    dataIndex: 'outNum',
//    width: 150,
//    edit: true,
//    editRule: async (text, record) => {
//      if (record.quantitativePackageFlag == 0) {
//        if (text <= 0) {
//          return '数量需要大于0';
//        } else if (text > record.currentNum) {
//          return '出库数量不能大于当前库存';
//        }
//      } else {
//        return '定数包管理物资数量不可修改';
//      }
//      return '';
//
//    },
//    editComponent: 'InputNumber',
//
//  },
    {
    title: '退库数量',
    dataIndex: 'outNum',
    width: 150,
    slots: { customRender: 'outNum' },
  },
    {
    title: '批号',
    dataIndex: 'batchNo',
    width: 150,
  },
  {
    title: '效期',
    dataIndex: 'term',
    width: 150,
    customRender: ({ text }) => {
      return !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
    },
  },
  {
    title: '单位',
    dataIndex: 'unitName',
    width: 100,
  },
  {
    title: '供应商',
    dataIndex: 'supplierName',
    width: 150,
  },
  {
    title: '生产厂家',
    dataIndex: 'manufacturerName',
    width: 150,
  },
];
//定义表格列
export const MaterialColumns: BasicColumn[] = [
  {
    title: '物资编码',
    dataIndex: 'goodsCode',
    width: 180,
    fixed: 'left',
  },
  {
    title: '物资名称',
    dataIndex: 'goodsName',
    width: 200,
    fixed: 'left',
  },
  {
    title: '规格型号',
    dataIndex: 'goodsSpecs',
    width: 180,
  },
  {
    title: '具体规格',
    dataIndex: 'goodsSpecsDetail',
    width: 180,
  },
  {
    title: '当前可用库存',
    dataIndex: 'currentNum',
    width: 150,
  },
    {
    title: '退库数量',
    dataIndex: 'outNum',
    width: 150,
    slots: { customRender: 'outNum' },
  },
  {
    title: '单位',
    dataIndex: 'unitName',
    width: 80,
  },
  {
    title: '科室库房',
    dataIndex: 'storageName',
    width: 150,
  },
  {
    title: '响应库房',
    dataIndex: 'responseStorageName',
    width: 150,
  },
  {
    title: '物资条码',
    dataIndex: 'uniqueCode',
    width: 220,
  },
  {
    title: '批次码',
    dataIndex: 'batchCode',
    width: 180,
  },
  {
    title: '批号',
    dataIndex: 'batchNo',
    width: 150,
  },
  {
    title: '效期',
    dataIndex: 'term',
    width: 150,
    customRender: ({ text }) => {
      return !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
    },
  },
  {
    title: '供应商',
    dataIndex: 'supplierName',
    width: 200,
  },
  {
    title: '生产厂家',
    dataIndex: 'manufacturerName',
    width: 200,
  },

];
