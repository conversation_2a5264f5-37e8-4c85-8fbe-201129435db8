<template>
  <div>
    <BasicModal @register="registerModal" destroyOnClose>
      <BasicTable @register="registerTable" :scroll="{ x: 1000, y: 1000 }" :columns="columns">
      </BasicTable>
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
import { ref, unref } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicTable, useTable } from '/@/components/Table';
import { list } from './DetailModal.api'
import { columns } from "./DetailModal.data";
//表单配置
const id = ref('')
const [registerTable] = useTable({
  api: (params) => list({ ...params, masterId: unref(id) }),
  isTreeTable: true,
  scroll: { y: 600 },
  showIndexColumn: false,
});
const [registerModal, { setModalProps }] = useModalInner(async (data) => {
  id.value = data.record.id
  setModalProps({
    showCancelBtn: !!data?.showFooter,
    showOkBtn: !!data?.showFooter,
    keyboard: false,
    maskClosable: true,
    footer: null,
    title: '详情',
    width: 1500,
  });
});
</script>

<style lang="less" scoped></style>