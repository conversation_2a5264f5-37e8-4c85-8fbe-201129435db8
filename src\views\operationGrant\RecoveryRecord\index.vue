<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <template #form-recoveryUser="{ model, field }">
        <ApiSelect
          :api="getSearchUserList"
          v-model:value="model[field]"
          allowClear
          showSearch
          placeholder="请选择回收人"
          optionFilterProp="label"
          labelField="realname"
          valueField="username"
          resultField="result"
          :params="searchParams"
          @search="onSearch"
          @click="onClick"
          :filterOption="false"
        >
        </ApiSelect>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" name="operationGrant-RecoveryRecord" setup>

import { BasicTable } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage'
import { columns, searchFormSchema } from './index.data';
import { list } from './index.api';
import { getSearchUserList } from '/@/api/common/api';
import { ApiSelect } from '/@/components/Form/index';
import { useApiSelect } from '/@/spdHooks/useApiSelect';
const { onSearch, onClick, searchParams } = useApiSelect();

//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    api: list,
    columns,
    canResize: false,
    scroll:{y:480},
    ellipsis: true,
    scroll:{y:500},
    showIndexColumn: true,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToNumber: [
      ],
      fieldMapToTime: [
        ['recoveryDate', ['recoveryDate_begin', 'recoveryDate_end'], 'YYYY-MM-DD']
      ],
    },
    showActionColumn: false,
    beforeFetch(params) {
      params.operationSuppliersStatus = 1
    }
  },
})

const [registerTable, { reload }, { selectedRowKeys }] = tableContext


</script>
<style scoped>
</style>
