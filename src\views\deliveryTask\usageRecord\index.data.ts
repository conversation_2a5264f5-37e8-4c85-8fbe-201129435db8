import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { getDepartListLeaf } from '/@/api/common/api';
import { getspdStoragelist } from './index.api';

//列表数据
export const columns: BasicColumn[] = [
  {
    title: '物资名称',
    align: 'center',
    dataIndex: 'goodsName',
    width:160,
    resizable: true,
  },
  {
    title: '物资编码',
    align: 'center',
    dataIndex: 'goodsCode',
    width:120,
    resizable: true,
    sorter: true,
  },
  {
    title: '物资条码',
    align: 'center',
    dataIndex: 'uniqueCode',
    width: 240,
    resizable: true,
    sorter: true,
  },
  {
    title: 'UDI',
    align: 'center',
    dataIndex: 'udiCode',
    width: 180,
    resizable: true,
    sorter: true,
  },
  {
    title: '规格',
    align: 'center',
    dataIndex: 'goodsSpecs',
    width: 100,
    resizable: true,
  },
  {
    title: '型号',
    align: 'center',
    dataIndex: 'goodsSpecsDetail',
    width: 100,
    resizable: true,
  },
  {
    title: '数量',
    align: 'center',
    dataIndex: 'totalNum',
    width: 100,
    resizable: true,
  },
  {
    title: '金额',
    align: 'center',
    dataIndex: 'totalAmt',
    width: 100,
    resizable: true,
  },
  {
    title: '出库仓库',
    align: 'center',
    dataIndex: 'responseStorageName',
    width: 120,
    resizable: true,
  },
  {
    title: '患者姓名',
    align: 'center',
    dataIndex: 'patientName',
    width: 100,
    resizable: true,
  },
  {
    title: '手术编号',
    align: 'center',
    dataIndex: 'operationSchedulingCode',
    width: 120,
    resizable: true,
  },
  {
    title: '手术科室',
    align: 'center',
    dataIndex: 'operationDepartName',
    width: 140,
    resizable: true,
  },
  {
    title: '领用人',
    align: 'center',
    dataIndex: 'operationUserName',
    width: 80,
    resizable: true,
  },
  {
    title: '生成日期',
    align: 'center',
    dataIndex: 'createTime',
    width: 140,
    resizable: true,
  },
  {
    title: '注册证号',
    align: 'center',
    dataIndex: 'registerNo',
    width: 140,
    resizable: true,
  },
  {
    title: '生产厂家',
    align: 'center',
    dataIndex: 'manufacturerName',
    width: 200,
    resizable: true,
  },


];
//查询数据
export const searchFormSchema: FormSchema[] = [
    {
    label: '物资名称',
    field: 'goodsName',
    component: 'JInput',
    colProps: { span: 6 },
  },
  {
    label: '物资编码',
    field: 'goodsCode',
    component: 'JInput',
    colProps: { span: 6 },
  },
  {
    label: '规格',
    field: 'goodsSpecs',
    component: 'JInput',
    colProps: { span: 6 },
  },
  {
    label: '型号',
    field: 'goodsSpecsDetail',
    component: 'JInput',
    colProps: { span: 6 },
  },
  {
    label: '物资条码',
    field: 'uniqueCode',
    component: 'JInput',
    colProps: { span: 6 },
  },
  {
    label: '出库仓库',
    field: 'responseStorageId',
    component: 'ApiSelect',
    componentProps: {
      api: getspdStoragelist,
      params: { storageType_MultiString: '1,2',},
      showSearch: true,
      labelField: 'storageName', // label值
      optionFilterProp: 'storageNameAbbr',
      valueField: 'id', // value值
      allowClear: true
    },
  },
  {
    label: '手术科室',
    field: 'operationDepartId',
    component: 'ApiSelect',
    componentProps: {
      api: getDepartListLeaf,
      params: {},
      showSearch: true,
      optionFilterProp: 'departNameDepartNameAbbr',
      labelField: 'departName',
      valueField: 'id',
      allowClear: true,
    },
  },
  {
    label: '生成日期',
    field: 'createTime',
    component: 'RangePicker',
    componentProps: { valueType: 'Date' }
  },
  {
    label: '手术编号',
    field: 'operationSchedulingCode',
    component: 'JInput',
    colProps: { span: 6 },
  },
  {
    label: '领用人',
    field: 'operationUserName',
    component: 'JInput',
    colProps: { span: 6 },
  },
  {
    label: '患者姓名',
    field: 'patientName',
    component: 'JInput',
    colProps: { span: 6 },
  },

];
