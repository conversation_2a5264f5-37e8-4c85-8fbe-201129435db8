import { defHttp } from '/@/utils/http/axios';
enum Api {
  list = '/spd/supplierAccount/list',
  push = '/spd/supplierAccount/push',
  build = '/spd/supplierAccount/build',
  queryAccount = '/spd/supplierAccount/queryAccount',
  importExcel = '/spd/supplierAccount/importExcel',
  export = '/spd/supplierAccount/export',
}

export const list = (params) => defHttp.get({ url: Api.list, params }); // 供应商对账-分页列表查询
export const push = (data) => defHttp.post({ url: Api.push, data }); // 供应商对账-推送
export const build = (data?) => defHttp.post({ url: Api.build, data }); // 供应商对账-构建
export const queryAccount = (params) => defHttp.get({ url: Api.queryAccount, params }); // 供应商对账-合计

export const importExcel = Api.importExcel; // 导入计费数据
export const exportExcel = (params) => defHttp.get({ url: Api.export, params }, { isReturnNativeResponse: true }); // 供应商对账-导出
