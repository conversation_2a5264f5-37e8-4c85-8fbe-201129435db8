<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection" class="ant-table-striped" :rowClassName="rowClassName">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleConsignment" v-auth="'user:Consignment2'"
          >生成代销出库单</a-button
        >
        <a-button type="primary" @click="handlePass">审核通过</a-button>
        <a-button type="primary" @click="handleNoPass" danger>审核不通过</a-button>
        <a-button type="primary" @click="barCodePrint">条码打印</a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!-- 详情 -->
    <DetailModel @register="registerModal" @success="handleSuccess"></DetailModel>
    <!-- 打印 -->
    <DistributionPrint :list="printList" ref="DistributionPrintRef"></DistributionPrint>
    <!-- 条码打印 -->
    <BarcodePrint @register="registerModal1" @success="handleSuccess"></BarcodePrint>
  </div>
</template>

<script lang="ts" name="operatetable-affirmlist" setup>
import { BasicTable, TableAction } from "/@/components/Table";
import { useListPage } from "/@/hooks/system/useListPage";
import { columns, searchFormSchema } from "./index.data";
import { list, audit, createConsignmentOutStore, print } from "./index.api";
import { useModal } from "/@/components/Modal/src/hooks/useModal";
import DetailModel from "./components/Detail/index.vue";
import { message } from "ant-design-vue";
import { computed, ref } from "vue";
import DistributionPrint from "./components/Print/DistributionPrint.vue";
import BarcodePrint from "./components/Print/BarcodePrint.vue";

//注册model
const [registerModal, { openModal }] = useModal();
const [registerModal1, { openModal: openModal1 }] = useModal();

const rowClassName = computed(() => {
  return (record, index: number) => (record.billFlag === 1 ? "darkRed" : "");
});

//注册table数据
const { tableContext } = useListPage({
  tableProps: {
    api: list,
    columns,
    canResize: true,
    showIndexColumn: true,
    formConfig: {
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToTime: [
        ["applyOrderTime", ["applyOrderTime_begin", "applyOrderTime_end"], "YYYY-MM-DD"],
        ["operationTime", ["operationTime_begin", "operationTime_end"], "YYYY-MM-DD"],
      ],
    },
    actionColumn: {
      width: 180,
      fixed: "right",
    },
    // 请求之前对参数做处理
    beforeFetch(params) {
      params.beginDate = params.beginDate == null ? null : params.beginDate + " 00:00:00";
      params.endDate = params.endDate == null ? null : params.endDate + " 23:59:59";
    },
  },
});

const [
  registerTable,
  { reload },
  { selectedRowKeys, selectedRows, rowSelection },
] = tableContext;

/**
 * 查看
 */
async function handleDetail(record: Recordable) {
  console.log("详情", record);
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}

//审核
function handleApproval(record: Recordable) {
  console.log("详情", record);
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: true,
  });
}
//打印

const printList = ref<any>([]);
const DistributionPrintRef = ref<any>(null);
const handlePrint = async (record) => {
  let params = <any>{
    confirmId: record.id, //依据单号
    pageSize: 8, // pageSize
  };
  printList.value = await print(params);
  setTimeout(function () {
    DistributionPrintRef.value.print();
  }, 200);
};

/**
 * 批量审核通过
 */
async function handlePass() {
  console.log("通过");
  let arr: any = [];
  for (let i = 0; i < selectedRows.value.length; i++) {
    if (selectedRows.value[i].auditStatus == 1) {
      for (let j = 0; j < selectedRowKeys.value.length; j++) {
        if (selectedRowKeys.value[j] == selectedRows.value[i].id) {
          arr.push(selectedRows.value[i].patientName);
          selectedRowKeys.value.splice(j, 1);
        }
      }
    }
  }
  if (arr.length > 0) {
    message.warning(arr.toString() + "的确认单已审核通过，不可重复审核");
  }
  let obj = {
    auditStatus: 1,
    ids: selectedRowKeys.value,
  };
  if (selectedRowKeys.value.length > 0) {
    await audit(obj);
    handleSuccess();
  }
}
/**
 * 批量审核不通过
 */
async function handleNoPass() {
  console.log("不通过");
  let arr: any = [];
  for (let i = 0; i < selectedRows.value.length; i++) {
    if (selectedRows.value[i].auditStatus == 1) {
      for (let j = 0; j < selectedRowKeys.value.length; j++) {
        if (selectedRowKeys.value[j] == selectedRows.value[i].id) {
          arr.push(selectedRows.value[i].patientName);
          selectedRowKeys.value.splice(j, 1);
        }
      }
    }
  }
  if (arr.length > 0) {
    message.warning(arr.toString() + "的确认单已审核通过，不可重复审核");
  }
  let obj = {
    auditStatus: 2,
    ids: selectedRowKeys.value,
  };
  if (selectedRowKeys.value.length > 0) {
    await audit(obj);
    handleSuccess();
  }
}
/**
 * 批量生成代销出库
 */
async function handleConsignment() {
  console.log("代销出库", selectedRowKeys.value);
  if (selectedRowKeys.value.length > 0) {
    await createConsignmentOutStore(selectedRowKeys.value);
    handleSuccess();
  }
}

async function barCodePrint() {
  console.log("条码打印", selectedRowKeys.value);
  if (selectedRowKeys.value.length > 0) {
    openModal1(true, {
      record: selectedRowKeys.value,
      isUpdate: true,
      showFooter: false,
    });
  } else {
    message.warning("请选择打印的确认单");
  }
}

/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}
/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: "计费详情",
      onClick: handleDetail.bind(null, record),
    },
    {
      label: "审核",
      onClick: handleApproval.bind(null, record),
      ifShow: (_action) => {
        return record.auditStatus !== 1; // 根据业务控制是否显示: 非enable状态的不显示启用按钮
      },
    },
    {
      label: "打印",
      onClick: handlePrint.bind(null, record),
    },
  ];
}
</script>

<style lang="scss" scoped>
.ant-table-striped :deep(.darkRed) td {
  color: rgb(241, 54, 54);
}
</style>
