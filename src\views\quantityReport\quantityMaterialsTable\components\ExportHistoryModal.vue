<template>
  <BasicModal @register="registerModal" destroyOnClose :maskClosable="false">
    <div style="display: flex;justify-content: space-evenly;padding-top: 50px;">
      <span class="text">上传文件:</span>
      <a-upload name="file" accept=".xls,.xlsx" :fileList="fileList" :remove="handleRemove"
        :beforeUpload="beforeUpload">
        <a-button class="export-btn" type="primary" preIcon="ant-design:upload-outlined">导入文件</a-button>
      </a-upload>
    </div>
    <template #footer>
      <div class="footer dpflex jcsb">
        <div></div>
        <div>
          <a-button @click="handleCancel">取消</a-button>
          <a-button :loading="loading" type="primary" @click="handleSubmit">确定</a-button>
        </div>
      </div>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, Ref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { useMessage } from '/@/hooks/web/useMessage';
import axios from 'axios';
import { getToken, getTenantId } from '/@/utils/auth';
import { uploadUrl } from '/@/hooks/setting/config';
const { createMessage, createWarningModal } = useMessage()
import { useGlobSetting } from '/@/hooks/setting';
const glob = useGlobSetting();
const emit = defineEmits(["success"]);
const importDlData = '/spd/dl/importDlData';

const fileList: Ref<File[]> = ref([]);
const loading = ref(false)
let formData = new FormData()
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (_data) => {
  clearFormData(formData);
  fileList.value = []
  setModalProps({
    width: 600,
  });
})
//上传前处理
const clearFormData = (formData) => {
  for (var key of formData.keys()) {
    formData.delete(key);
  }
}
//移除上传文件
const handleRemove = (file) => {
  const index = unref(fileList).indexOf(file);
  const newFileList = unref(fileList).slice();
  newFileList.splice(index, 1);
  fileList.value = newFileList;
}
//上传前
const beforeUpload = (file) => {
  clearFormData(formData);
  formData.append('file', file);
  fileList.value = [...unref(fileList), file];
  return false;
}

const handleImport = () => {
  return new Promise((resolve, reject) => {
    axios.post(uploadUrl.uploadUrl + importDlData, formData, {
      headers: {
        'X-Access-Token': getToken(),
        'Tenant-Id': getTenantId()
      },
    }).then(res => {
      if (res.data.code === 201) {
        let {
          message,
          result: { msg, fileUrl, fileName },
        }: any = res.data;
        let href = glob.uploadUrl + fileUrl;
        createWarningModal({
          title: message,
          centered: false,
          content:
            `<div>
              <span>${msg}</span><br/> 
              <span>具体详情请<a href = ${href} download = ${fileName}> 点击下载 </a> </span> 
            </div>
          `,
        });
      }
      resolve(res)
    }).catch(err => {
      reject(err)
    })
  })
}
const handleCancel = () => {
  closeModal()
}
const handleSubmit = async () => {

  try {
    if (!unref(fileList).length) return createMessage.error('请上传文件')
    loading.value = true
    const res: any = await handleImport()
    if (res.data.code === 200) {
      emit('success',)
      createMessage.success(res?.data?.message);
      closeModal()
    } else {
      createMessage.error(res?.data?.message);
    }
  } catch (err) {
    console.log(err, 'err');
  } finally {
    loading.value = false
  }
}
</script>
<style lang="scss" scoped>
.text {
  font-size: 16px;

  &::after {
    // 添加 ::after 伪元素的属性
    content: '*';
    position: absolute;
    left: 110px;
    color: red;
  }
}

:deep(.ant-col) {
  width: auto;
}


.export-btn {
  width: 200px;
}
</style>
